close all;


first=readtable("E:\apersonal\更改固件后采集\8_22\1.csv");
plotgh(first);
first1=readtable("E:\apersonal\更改固件后采集\8_22\2.csv");
plotgh(first1);
first2=readtable("E:\apersonal\更改固件后采集\8_22\3.csv");
plotgh(first2);
first3=readtable("E:\apersonal\更改固件后采集\8_22\4.csv");
plotgh(first3);
first4=readtable("E:\apersonal\更改固件后采集\8_22\5.csv");
plotgh(first4);
first5=readtable("E:\apersonal\更改固件后采集\8_22\6.csv");
plotgh(first5);

first6=readtable("E:\apersonal\更改固件后采集\8_22\绕圈-1新固件.csv");
plotgh(first6);
first7=readtable("E:\apersonal\更改固件后采集\8_22\绕圈2.csv");
plotgh(first7);


first=readtable("E:\apersonal\更改固件后采集\第一次\15min.csv");
plotgh(first);
first1=readtable("E:\apersonal\更改固件后采集\第一次\直线1.csv");
plotgh(first1);
first2=readtable("E:\apersonal\更改固件后采集\第一次\直线12.csv");
plotgh(first2);




first=readtable("E:\apersonal\更改固件后采集\烧录后第一次.csv");
plotgh(first);
first1=readtable("E:\apersonal\更改固件后采集\烧录后第二次采集.csv");
plotgh(first1);
first2=readtable("E:\apersonal\更改固件后采集\第一次采集.csv");
plotgh(first2);
first3=readtable("E:\apersonal\更改固件后采集\第二次采集.csv");
plotgh(first3);
first4=readtable("E:\apersonal\更改固件后采集\8_21\第一圈大圈.csv");
plotgh(first4);

function feedback()
feedback13 = readtable('E:\apersonal\组合算法后处理VS\组合算法后处理VS\POST_Raw_Version_LiGong\data\对比文件\标定对比\反馈9.txt', ...
        'HeaderLines', 1, 'ReadVariableNames', false);
% 将表格数据转换为矩阵（提高访问速度）
dataMatrix = table2array(feedback13);

% 提取所有需要的列（一次性提取比循环中提取效率高）
cols1 = [25, 26, 27];  % 第一组列
cols2 = [31, 32, 33];  % 第二组列

% 向量化计算
% 计算第一组列的平方和的平方根（欧几里得范数）
sumOfSquares1 = sqrt(sum(dataMatrix(:, cols1).^2, 2));

% 计算第二组列的平方和
sumOfSquares2 = sqrt(sum(dataMatrix(:, cols2).^2, 2));

% 合并结果
sumOfSquares = [sumOfSquares1, sumOfSquares2];

% 绘图（修正了原代码中的错误：应该用sumOfSquares(:,1)和sumOfSquares(:,2)）
figure;
plot(1:size(sumOfSquares, 1), sumOfSquares(:,1), '.r-', 'DisplayName', '优化前');
hold on;
plot(1:size(sumOfSquares, 1), sumOfSquares(:,2), '.b-', 'DisplayName', '优化后');
hold off;
legend;
xlabel('数据点');
ylabel('计算值');
title('计算结果对比');
end
function plotgh(first)


figure;subplot(2, 1, 1);
plot(first{end,55}, first{end,56}, 'ro', 'MarkerSize', 8, 'HandleVisibility', 'off'); % 红色圆圈标记第55列最后一个点
plot(first{end,14}, first{end,15}, 'bo', 'MarkerSize', 8, 'HandleVisibility', 'off'); % 蓝色圆圈标记第14列最后一个点
plot(first{first{:,55}~=0,55}, first{first{:,55}~=0,56}, '.-r', 'DisplayName','解算结果平面图');hold on;legend;
plot(first{first{:,14}~=0,14}, first{first{:,15}~=0,15}, '.-b', 'DisplayName','解算结果平面图');hold on;legend;

[yjx,yjy,yjz]=llh2enu(first,14,15,16);
[yjx1,yjy1,yjz1]=llh2enu(first,55,56,57);
subplot(2, 1, 2);
plot(yjx,yjy,'.b-', 'DisplayName','gnss');hold on;
plot(yjx1,yjy1,'.r-', 'DisplayName','解算结果');hold on;legend;
plot(yjx(end), yjy(end), 'bo', 'MarkerSize', 8, 'LineWidth', 2, 'DisplayName', 'gnss终点');
plot(yjx1(end), yjy1(end), 'ro', 'MarkerSize', 8, 'LineWidth', 2, 'DisplayName', '算法结果终点');
hold on;
figure;
subplot(3, 1, 1);plot(1:length(first{:,61}), first{:,61}, '.r', 'DisplayName','算法结果');hold on;
plot(1:length(first{:,20}), first{:,20}, '.b', 'DisplayName','算法结果');hold on;legend;
title("解算姿态对比");legend;
subplot(3, 1, 2);plot(1:length(first{:,62}), first{:,62}, '.r', 'DisplayName','算法结果');hold on;
plot(1:length(first{:,21}), first{:,21}, '.b', 'DisplayName','算法结果');hold on;legend;
subplot(3, 1, 3);plot(1:length(first{:,63}), first{:,63}, '.r', 'DisplayName','算法结果');hold on;
plot(1:length(first{:,22}), first{:,22}, '.b', 'DisplayName','算法结果');hold on;legend;
 
figure;
subplot(3, 1, 1);plot(1:length(first{:,58}), first{:,58}, '.r', 'DisplayName','算法结果');hold on;
plot(1:length(first{:,17}), first{:,17}, '.b', 'DisplayName','算法结果');hold on;legend;
title("解算速度对比");legend;
subplot(3, 1, 2);plot(1:length(first{:,59}), first{:,59}, '.r', 'DisplayName','算法结果');hold on;
plot(1:length(first{:,18}), first{:,18}, '.b', 'DisplayName','算法结果');hold on;legend;
subplot(3, 1, 3);plot(1:length(first{:,60}), first{:,60}, '.r', 'DisplayName','算法结果');hold on;
plot(1:length(first{:,19}), first{:,19}, '.b', 'DisplayName','算法结果');hold on;legend;    

end



function [E, N, U] = llh2enu(resdata,lat_deg, lon_deg, h)
    DEG2RAD = pi/180;
    R = 6378137;  % 地球半径(米)
    lat_rad=resdata{:,lat_deg}* DEG2RAD;
    lon_rad = resdata{:,lon_deg}* DEG2RAD;
    h_rad=resdata{:,h};

    lat0_rad=resdata{1,lat_deg}* DEG2RAD;
    lon0_rad=resdata{1,lon_deg}* DEG2RAD;
    h0=resdata{1,h};

   % lat_rad = lat_deg(:) * DEG2RAD;  % 转为列向量+弧度
   % lon_rad = lon_deg(:) * DEG2RAD;
    dLat = lat_rad - lat0_rad;
    dLon = lon_rad - lon0_rad;
    E = (R + h_rad(:)) .* cos(lat0_rad) .* dLon;
    N = (R + h_rad(:)) .* dLat;
    U = h_rad(:) - h0;
end
