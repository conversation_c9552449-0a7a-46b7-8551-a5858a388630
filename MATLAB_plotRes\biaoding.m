close all;
first=readtable("E:\apersonal\carslow\第一组.csv");
%second=readtable("E:\apersonal\carslow\第二组.csv");
first1=readtable("E:\apersonal\carslow\第三组.csv");
second1=readtable("E:\apersonal\carslow\第四组绕圈.csv");
first2=readtable("E:\apersonal\carslow\第五组.csv");
second2=readtable("E:\apersonal\carslow\第六组.csv");

figduibi(first);
%figduibi(second);
figduibi(first1);
figduibi(second1);
figduibi(first2);
figduibi(second2);
figure;plot(first{:,55}, first{:,56}, '.-r', 'DisplayName','输出增加杆臂补偿');hold on;
figure;
subplot(3, 1, 1);plot(1:length(first{:,61}), first{:,61}, '.r', 'DisplayName','算法结果');hold on;
plot(1:length(first{:,20}), first{:,20}, '.b', 'DisplayName','算法结果');hold on;
subplot(3, 1, 2);plot(1:length(first{:,62}), first{:,62}, '.r', 'DisplayName','算法结果');hold on;
plot(1:length(first{:,21}), first{:,21}, '.b', 'DisplayName','算法结果');hold on;
subplot(3, 1, 3);plot(1:length(first{:,63}), first{:,63}, '.r', 'DisplayName','算法结果');hold on;
plot(1:length(first{:,22}), first{:,22}, '.b', 'DisplayName','算法结果');hold on;




feedback13 = readtable('E:\apersonal\组合算法后处理VS\组合算法后处理VS\POST_Raw_Version_LiGong\data\对比文件\标定对比\反馈9.txt', ...
        'HeaderLines', 1, 'ReadVariableNames', false);

% 将表格数据转换为矩阵（提高访问速度）
dataMatrix = table2array(feedback13);

% 提取所有需要的列（一次性提取比循环中提取效率高）
cols1 = [25, 26, 27];  % 第一组列
cols2 = [31, 32, 33];  % 第二组列

% 向量化计算
% 计算第一组列的平方和的平方根（欧几里得范数）
sumOfSquares1 = sqrt(sum(dataMatrix(:, cols1).^2, 2));

% 计算第二组列的平方和
sumOfSquares2 = sqrt(sum(dataMatrix(:, cols2).^2, 2));

% 合并结果
sumOfSquares = [sumOfSquares1, sumOfSquares2];

% 绘图（修正了原代码中的错误：应该用sumOfSquares(:,1)和sumOfSquares(:,2)）
figure;
plot(1:size(sumOfSquares, 1), sumOfSquares(:,1), '.r-', 'DisplayName', '优化前');
hold on;
plot(1:size(sumOfSquares, 1), sumOfSquares(:,2), '.b-', 'DisplayName', '优化后');
hold off;
legend;
xlabel('数据点');
ylabel('计算值');
title('计算结果对比');
function figduibi(second)
figure;
plot(second{:,55}, second{:,56}, '.-b', 'DisplayName', '输出增加杆臂补偿（第二组）');
figure;
subplot(3, 1, 1);
plot(1:length(second{:,61}), second{:,61}, '.r', 'DisplayName', '算法结果（第二组）');hold on;
plot(1:length(second{:,20}), second{:,20}, '.b', 'DisplayName','算法结果');hold on;
subplot(3, 1, 2);
plot(1:length(second{:,62}), second{:,62}, '.r', 'DisplayName', '算法结果（第二组）');hold on;
plot(1:length(second{:,21}), second{:,21}, '.b', 'DisplayName','算法结果');hold on;
subplot(3, 1, 3);
plot(1:length(second{:,63}), second{:,63}, '.r', 'DisplayName', '算法结果（第二组）');hold on;
plot(1:length(second{:,22}), second{:,22}, '.b', 'DisplayName','算法结果');hold on;

figure;
subplot(3, 1, 1);
plot(1:length(second{:,17}), second{:,17}, '.r', 'DisplayName', '算法结果（第二组）');hold on;
plot(1:length(second{:,58}), second{:,58}, '.b', 'DisplayName','算法结果');hold on;
subplot(3, 1, 2);
plot(1:length(second{:,19}), second{:,18}, '.r', 'DisplayName', '算法结果（第二组）');hold on;
plot(1:length(second{:,59}), second{:,59}, '.b', 'DisplayName','算法结果');hold on;
subplot(3, 1, 3);
plot(1:length(second{:,19}), second{:,19}, '.r', 'DisplayName', '算法结果（第二组）');hold on;
plot(1:length(second{:,60}), second{:,60}, '.b', 'DisplayName','算法结果');hold on;
end
