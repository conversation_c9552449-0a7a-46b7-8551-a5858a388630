close all;
ResDatatemp = readtable('../POST_Raw_Version_LiGong/data/验前观测方程8.txt', ...
        'HeaderLines', 1, 'ReadVariableNames', false);
yuanjidatatemp = readtable( 'E:\apersonal\组合算法后处理VS\data_collection\truck\daoyuan\SN_2025_0731_1536_52_RESULT.csv',...
'HeaderLines', 1, 'ReadVariableNames', false); 
%first=readtable("E:/apersonal/更改固件后采集/8_22/1.csv");
first=readtable("E:/apersonal/更改固件后采集/8_22/6.csv");
%'D:\desktop\组合算法后处理VS\组合算法后处理VS\POST_Raw_Version_LiGong\data\data_collection\truck\daoyuan\测试数据\原极设备绕圈测试数据\直线-快速_RESULT.csv', ...
        
DEG2RAD = pi/180;
R = 6378137;  % 地球半径(米)
figure('Name','经纬度高');
hold on; grid on;
plot(ResDatatemp{:,26}, ResDatatemp{:,25}, '.-r', 'DisplayName','输出增加杆臂补偿');hold on;
plot(first{:,55}, first{:,56}, '.-k', 'DisplayName','上机输出结果');hold on;
plot(ResDatatemp{ResDatatemp{:,35}~=0,35}, ResDatatemp{ResDatatemp{:,34}~=0,34}, '.-b', 'DisplayName','gnss');hold on;
%plot(yuanjidatatemp{:,5},yuanjidatatemp{:,6}, '.-g', 'DisplayName','原极算法结果');hold on;
xlabel('经度(°)'); ylabel('纬度(°)'); title('经纬度轨迹'); legend;

common=find(ResDatatemp{:,35}~=0);
commonyuji=find(yuanjidatatemp{:,24}~=0);
[m61x,m61y,m61z]=llh2enu(ResDatatemp,25,26,27,R,DEG2RAD);
[yjx,yjy,yjz]=llh2enu(yuanjidatatemp,6,5,7,R,DEG2RAD);
[m61_gnssx,m61_gnssy,m61_gnssz]=llh2enu2(ResDatatemp{common,34},ResDatatemp{common,35},ResDatatemp{common,36},ResDatatemp{common(1),34},ResDatatemp{common(1),35},ResDatatemp{common(1),36},R,DEG2RAD);
[yj_gnssx,yj_gnssy,yj_gnssz]=llh2enu2(yuanjidatatemp{commonyuji,25},yuanjidatatemp{commonyuji,24},yuanjidatatemp{commonyuji,26},yuanjidatatemp{commonyuji(1),25},yuanjidatatemp{commonyuji(1),24},yuanjidatatemp{commonyuji(1),26},R,DEG2RAD);
[prix,priy,priz]=llh2enu(first,56,55,57,R,DEG2RAD);


figure;
plot(m61x,m61y,'.r-', 'DisplayName','算法结果');hold on
plot(m61_gnssx,m61_gnssy,'.b-', 'DisplayName','gnss');hold on;
plot(prix,priy,'.k-', 'DisplayName','gnss');hold on;
plot(m61x(end), m61y(end), 'ro', 'MarkerSize', 8, 'LineWidth', 2, 'DisplayName', '算法结果终点');
plot(m61_gnssx(end), m61_gnssy(end), 'bo', 'MarkerSize', 8, 'LineWidth', 2, 'DisplayName', 'gnss终点');
plot(prix(end), priy(end), 'ko', 'MarkerSize', 8, 'LineWidth', 2, 'DisplayName', '文件输出终点');
xlabel('x(m)'); ylabel('y(m)'); title('平面轨迹'); legend;

%天向对比
figure;hold on;subplot(2, 1, 1);
plot(1:length(ResDatatemp{:,27}),m61x,'.r-', 'DisplayName','算法结果');hold on
plot(common,m61_gnssx,'.b-', 'DisplayName','gnss');hold on;
xlabel('历元'); ylabel('x(m)'); title('ins600m21A东向误差分析'); legend;
yyaxis right;
diff61x=m61x(common)-m61_gnssx;
plot(common,diff61x,'.k-', 'DisplayName','东向差值');
subplot(2, 1, 2);
plot(1:length(yuanjidatatemp{:,5}),yjx,'.r', 'DisplayName','算法结果');hold on
plot(commonyuji,yj_gnssx,'.b', 'DisplayName','gnss');hold on;
xlabel('历元'); ylabel('x(m)'); title('原极东向误差分析'); legend;
yyaxis right;
diffyjx=yjx(commonyuji)-yj_gnssx;
plot(commonyuji,diffyjx,'.k', 'DisplayName','东向差值');

%动向对比
figure;hold on;subplot(2, 1, 1);
plot(1:length(ResDatatemp{:,27}),m61y,'.r-', 'DisplayName','算法结果');hold on
plot(common,m61_gnssy,'.b-', 'DisplayName','gnss');hold on;
xlabel('历元'); ylabel('y(m)'); title('ins600m21A北向误差分析'); legend;
yyaxis right;
diff61y=m61y(common)-m61_gnssy;
plot(common,diff61y,'.k-', 'DisplayName','北向差值');
subplot(2, 1, 2);
plot(1:length(yuanjidatatemp{:,5}),yjy,'.r', 'DisplayName','算法结果');hold on
plot(commonyuji,yj_gnssy,'.b', 'DisplayName','gnss');hold on;
xlabel('历元'); ylabel('y(m)'); title('原极北向误差分析'); legend;
yyaxis right;
diffyjy=yjy(commonyuji)-yj_gnssy;
plot(commonyuji,diffyjy,'.k', 'DisplayName','北向差值');

%高度对比
figure;hold on;subplot(2, 1, 1);
plot(1:length(ResDatatemp{:,27}), ResDatatemp{:,27}, '.r-', 'DisplayName','算法结果');hold on;
plot(common, ResDatatemp{common,36}, '.b-', 'DisplayName','gnss');hold on;
xlabel('历元'); ylabel('高度(m)'); title('ins600m21A高度分析'); legend;
yyaxis right;
diff_h=ResDatatemp{common,27}-ResDatatemp{common,36};
plot(common, diff_h,'.k', 'DisplayName','高度差值');hold on;
ylabel('高度差(m)');
subplot(2, 1, 2);
plot(1:length(yuanjidatatemp{:,7}), yuanjidatatemp{:,7}, '.r', 'DisplayName','算法结果');hold on;
plot(commonyuji, yuanjidatatemp{commonyuji,26}, '.b', 'DisplayName','gnss');hold on;
xlabel('历元'); ylabel('高度(m)'); title('原极高度分析'); legend;
yyaxis right;
diffh2=yuanjidatatemp{commonyuji,7}-yuanjidatatemp{commonyuji,26};
plot(commonyuji, diffh2,'.k', 'DisplayName','高度差值');hold on;

%动向速度对比
figure;hold on;subplot(2, 1, 1);
plot(1:length(ResDatatemp{:,40}), ResDatatemp{:,40}, '.r', 'DisplayName','算法结果');hold on;
plot(common, ResDatatemp{common,37}, '.b', 'DisplayName','gnss');hold on;
xlabel('历元'); ylabel('Ve(m/s)'); title('ins600m21A动向速度分析'); legend;
yyaxis right;
diff_ve=ResDatatemp{common,40}-ResDatatemp{common,37};
plot(common, diff_ve,'.k', 'DisplayName','动向速度差值');hold on;
ylabel('动向速度差值(m/s)');
subplot(2, 1, 2);
plot(1:length(yuanjidatatemp{:,13}), yuanjidatatemp{:,13}, '.r', 'DisplayName','算法结果');hold on;
plot(commonyuji, yuanjidatatemp{commonyuji,28}, '.b', 'DisplayName','gnss');hold on;
xlabel('历元'); ylabel('Ve(m/s)'); title('原极动向速度分析'); legend;
yyaxis right;
diffh2ve=yuanjidatatemp{commonyuji,13}-yuanjidatatemp{commonyuji,28};
plot(commonyuji, diffh2ve,'.k', 'DisplayName','东向速度差值');hold on;

%北向速度对比
figure;hold on;subplot(2, 1, 1);
plot(1:length(ResDatatemp{:,41}), ResDatatemp{:,41}, '.r', 'DisplayName','算法结果');hold on;
plot(common, ResDatatemp{common,38}, '.b', 'DisplayName','gnss');hold on;
xlabel('历元'); ylabel('Ve(m/s)'); title('ins600m21A北向速度分析'); legend;
yyaxis right;
diff_vn=ResDatatemp{common,41}-ResDatatemp{common,38};
plot(common, diff_vn,'.k', 'DisplayName','北向速度差值');hold on;
ylabel('北向速度差值(m/s)');
subplot(2, 1, 2);
plot(1:length(yuanjidatatemp{:,12}), yuanjidatatemp{:,12}, '.r', 'DisplayName','算法结果');hold on;
plot(commonyuji, yuanjidatatemp{commonyuji,27}, '.b', 'DisplayName','gnss');hold on;
xlabel('历元'); ylabel('Ve(m/s)'); title('原极北向速度分析'); legend;
yyaxis right;
diffh2vn=yuanjidatatemp{commonyuji,12}-yuanjidatatemp{commonyuji,27};
plot(commonyuji, diffh2vn,'.k', 'DisplayName','北向速度差值');hold on;

%天向速度对比
figure;hold on;subplot(2, 1, 1);
plot(1:length(ResDatatemp{:,42}), ResDatatemp{:,42}, '.r', 'DisplayName','算法结果');hold on;
plot(common, ResDatatemp{common,39}, '.b', 'DisplayName','gnss');hold on;
xlabel('历元'); ylabel('Ve(m/s)'); title('ins600m21A天向速度分析'); legend;
yyaxis right;
diff_vu=ResDatatemp{common,42}-ResDatatemp{common,39};
plot(common, diff_vu,'.k', 'DisplayName','天向速度差值');hold on;
ylabel('北向速度差值(m/s)');
subplot(2, 1, 2);
plot(1:length(yuanjidatatemp{:,14}), yuanjidatatemp{:,14}, '.r', 'DisplayName','算法结果');hold on;
plot(commonyuji, yuanjidatatemp{commonyuji,29}, '.b', 'DisplayName','gnss');hold on;
xlabel('历元'); ylabel('Ve(m/s)'); title('原极天向速度分析'); legend;
yyaxis right;
diffh2vu=yuanjidatatemp{commonyuji,14}-yuanjidatatemp{commonyuji,29};
plot(commonyuji, diffh2vu,'.k', 'DisplayName','天向速度差值');hold on;

%三个姿态角对比
figure;hold on;subplot(2, 1, 1);
plot(1:length(ResDatatemp{:,31}), ResDatatemp{:,31}, '.r', 'DisplayName','算法结果');hold on;
plot(common, ResDatatemp{common,28}, '.b', 'DisplayName','gnss');hold on;
xlabel('历元'); ylabel('Pitch(°)'); title('ins600m21A Pitch分析'); legend;
yyaxis right;
diff_pitch=ResDatatemp{common,31}-ResDatatemp{common,28};
plot(common, diff_pitch,'.k', 'DisplayName','Pitch差值');hold on;
ylabel('PITCH差值(m/s)');
subplot(2, 1, 2);
plot(1:length(yuanjidatatemp{:,9}), yuanjidatatemp{:,9}, '.r', 'DisplayName','算法结果');hold on;
xlabel('历元'); ylabel('Pitch(°)');  title('原极 Pitch分析'); legend;legend;

figure;hold on;subplot(2, 1, 1);
plot(1:length(ResDatatemp{:,32}), ResDatatemp{:,32}, '.r', 'DisplayName','算法结果');hold on;
plot(common, ResDatatemp{common,29}, '.b', 'DisplayName','gnss');hold on;
xlabel('历元'); ylabel('roll(°)'); title('ins600m21A roll(°)'); legend;
yyaxis right;
diff_roll=ResDatatemp{common,32}-ResDatatemp{common,29};
plot(common, diff_roll,'.k', 'DisplayName','天向速度差值');hold on;
ylabel('roll差值(°)');
subplot(2, 1, 2);
plot(1:length(yuanjidatatemp{:,10}), yuanjidatatemp{:,10}, '.r', 'DisplayName','算法结果');hold on;
xlabel('历元'); ylabel('roll(°)');  legend; title('原极 ROLL分析'); legend;legend;

figure;hold on;subplot(2, 1, 1);
plot(1:length(ResDatatemp{:,33}), ResDatatemp{:,33}, '.r', 'DisplayName','算法结果');hold on;
plot(common, ResDatatemp{common,30}, '.b', 'DisplayName','gnss');hold on;
xlabel('历元'); ylabel('heading(°)'); title('ins600m21A Heading分析'); legend;
yyaxis right;
diff_heading=ResDatatemp{common,33}-ResDatatemp{common,30};
plot(common, diff_heading,'.k', 'DisplayName','天向速度差值');hold on;
ylabel('Heading差值(m/s)');
subplot(2, 1, 2);
plot(1:length(yuanjidatatemp{:,8}), yuanjidatatemp{:,8}, '.r', 'DisplayName','算法结果');hold on;
xlabel('历元'); ylabel('heading(°)');  legend;title('原极 Heading分析'); legend;legend;
aa=0;


function [E, N, U] = llh2enu(resdata,lat_deg, lon_deg, h,  R, DEG2RAD)
    lat_rad=resdata{:,lat_deg}* DEG2RAD;
    lon_rad = resdata{:,lon_deg}* DEG2RAD;
    h_rad=resdata{:,h};

    lat0_rad=resdata{1,lat_deg}* DEG2RAD;
    lon0_rad=resdata{1,lon_deg}* DEG2RAD;
    h0=resdata{1,h};

   % lat_rad = lat_deg(:) * DEG2RAD;  % 转为列向量+弧度
   % lon_rad = lon_deg(:) * DEG2RAD;
    dLat = lat_rad - lat0_rad;
    dLon = lon_rad - lon0_rad;
    E = (R + h_rad(:)) .* cos(lat0_rad) .* dLon;
    N = (R + h_rad(:)) .* dLat;
    U = h_rad(:) - h0;
end
function [E, N, U] = llh2enu2(lat_deg, lon_deg, h, lat0_rad, lon0_rad, h0, R, DEG2RAD)
    lat0_rad=lat0_rad* DEG2RAD;
    lon0_rad=lon0_rad* DEG2RAD;
    lat_rad = lat_deg(:) * DEG2RAD;  % 转为列向量+弧度
    lon_rad = lon_deg(:) * DEG2RAD;
    dLat = lat_rad - lat0_rad;
    dLon = lon_rad - lon0_rad;
    E = (R + h(:) ).* cos(lat0_rad) .* dLon;
    N = (R + h(:)) .* dLat;
    U = h(:) - h0;
end