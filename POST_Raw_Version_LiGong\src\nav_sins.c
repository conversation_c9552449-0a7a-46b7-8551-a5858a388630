/***********************************************************************************
nav sins module
All rights reserved I-NAV 2023 2033
***********************************************************************************/
/***********************************************************************************
Modification history

|-----------+---------------+-------------------|
|Author          |    Date               |    Done                      |
|-----------+---------------+-------------------|
|DengWei       |  2023-2-4          | First Creation             |
|-----------+---------------+-------------------|  
***********************************************************************************/
#include "nav_includes.h"

//SINS_t SINS_Data = {0};
/******************************************************************************
*原  型：void Earth_Init(_NAV_Data_Full_t* NAV_Data_Full_p)
*功  能：大地数据初始化
*输  入： 
*输  出：无
*******************************************************************************/
void Earth_Init(_NAV_Data_Full_t* NAV_Data_Full_p)
{
	int i;
	
	NAV_Data_Full_p->EARTH.cL = 0.0;
	NAV_Data_Full_p->EARTH.sL = 0.0;
	NAV_Data_Full_p->EARTH.tL = 0.0;
	NAV_Data_Full_p->EARTH.RMh = 0.0;
	NAV_Data_Full_p->EARTH.RNh = 0.0;
	//NAV_Data_Full_p->EARTH.g = G0;
    NAV_Data_Full_p->EARTH.Wie = WIE;
	//NAV_Data_Full_p->EARTH.Re0 = RE_WGS84;
	for (i = 0; i < 3; i++)
	{
		NAV_Data_Full_p->EARTH.wnen[i] = 0.0;
		NAV_Data_Full_p->EARTH.wnie[i] = 0.0;
		NAV_Data_Full_p->EARTH.wnien[i] = 0.0;
		NAV_Data_Full_p->EARTH.wnin[i] = 0.0;
		NAV_Data_Full_p->EARTH.gcc[i] = 0.0;
		NAV_Data_Full_p->EARTH.gn[i] = 0.0;
	}
	NAV_Data_Full_p->EARTH.gn[2] = -G0;
}

void CalEarthGn(double *pos,double *gn)
{
	//参考《捷联惯导算法与组合导航原理》公式3.2.23
	//gl=g0(1+beta*sinL*sinL-beta1*sin2L*sin2L)-beta2*h
	//sin2L=2*sinL*cosL
	//其中beta=5.30240e-3
	//beta1=5.82e-6
	//beta2=2u/(R*R*R)=3.086e-6
	//beta3=8.08e-9
	double sL=0.0;
	double cL=0.0;
	double s2L=0.0;
	sL	=	sin(pos[0]);
	cL	=	cos(pos[0]);
	s2L	=	2*sL*cL;
	
	gn[0]	=	0.0;
	gn[1]	=	-(8.08e-9*pos[2] * s2L);
	gn[2]	=	-( G0 * (1 + 5.30240e-3*sL *sL - 5.82e-6 * s2L * s2L)- 3.086e-6 * pos[2]);
}

/******************************************************************************
*原  型：static void Earth_UP(_NAV_Data_Full_t* NAV_Data_Full_p)  
*功  能：//更新大地数据
*输  入：维度、经度、高程及导航坐标系下东北天速度
*输  出：导航系相对大地坐标系参数
*******************************************************************************/
void Earth_UP(_NAV_Data_Full_t* NAV_Data_Full_p,double *tmp_pos, double *tmp_vn)  //更新大地数据
{
	unsigned char i = 0;
	double pos[3] = {0};
	double vn[3] = {0};
	double sq, sq2,sq2_1;
	double temp[3];
	for(i = 0;i<3;i++)
	{
		pos[i] = tmp_pos[i];
		vn[i] =  tmp_vn[i];
	}
	
	NAV_Data_Full_p->EARTH.sL = sin(pos[0]);
	NAV_Data_Full_p->EARTH.cL = cos(pos[0]);
	NAV_Data_Full_p->EARTH.tL = NAV_Data_Full_p->EARTH.sL / NAV_Data_Full_p->EARTH.cL;

	//sq=1-e2sin2L
	sq = 1 - E_WGS84 * E_WGS84 * NAV_Data_Full_p->EARTH.sL * NAV_Data_Full_p->EARTH.sL;
	sq2 = sqrt(sq);
	sq2_1 = sq*sq2;
	//子午圈主曲率半径
	NAV_Data_Full_p->EARTH.RMh = pos[2] + RE_WGS84 * (1 - E_WGS84 * E_WGS84) / sq2_1;
	//卯酉圈主曲率半径
	NAV_Data_Full_p->EARTH.RNh = pos[2] + RE_WGS84 / sq2;
	NAV_Data_Full_p->EARTH.clRNh = NAV_Data_Full_p->EARTH.cL * NAV_Data_Full_p->EARTH.RNh;
	//地球自转引起的角速度在导航系下坐标值，地球自东向西旋转，东向为0
	NAV_Data_Full_p->EARTH.wnie[0] = 0.0;
	NAV_Data_Full_p->EARTH.wnie[1] = WIE * NAV_Data_Full_p->EARTH.cL;
	NAV_Data_Full_p->EARTH.wnie[2] = WIE * NAV_Data_Full_p->EARTH.sL;
	//导航系速度计算大地坐标系下角速度
	NAV_Data_Full_p->EARTH.wnen[0] = -vn[1] / NAV_Data_Full_p->EARTH.RMh;
	NAV_Data_Full_p->EARTH.wnen[1] = vn[0] / NAV_Data_Full_p->EARTH.RNh;
	NAV_Data_Full_p->EARTH.wnen[2] = NAV_Data_Full_p->EARTH.wnen[1] * NAV_Data_Full_p->EARTH.tL;
	//载体在惯性坐标系下的角速度
	matrixSum(NAV_Data_Full_p->EARTH.wnie, NAV_Data_Full_p->EARTH.wnen, 3, 1, 1, NAV_Data_Full_p->EARTH.wnin);

	//计算导航坐标系下载体当前位置重力加速度
#if 1	
	//grs80重力模型
	NAV_Data_Full_p->EARTH.gn[2] = -(  G0 * (1 + 5.27094e-3 * NAV_Data_Full_p->EARTH.sL * NAV_Data_Full_p->EARTH.sL
                                        	   + 2.32718e-5 * NAV_Data_Full_p->EARTH.sL * NAV_Data_Full_p->EARTH.sL * NAV_Data_Full_p->EARTH.sL * NAV_Data_Full_p->EARTH.sL)
									  - 3.086e-6 * pos[2]);
#endif
	//CalEarthGn(pos,NAV_Data_Full_p->EARTH.gn);

	//计算有害加速度gcc
	////参考《捷联惯导算法与组合导航原理》公式4.1.20
	//gcc=-(2wnie+wnen)*vn+gn=-(wnie+wnin)*vn+gn
	matrixSum(NAV_Data_Full_p->EARTH.wnie, NAV_Data_Full_p->EARTH.wnin, 3, 1, 1, NAV_Data_Full_p->EARTH.wnien);
	cross3(NAV_Data_Full_p->EARTH.wnien, vn, temp);
	matrixSum(NAV_Data_Full_p->EARTH.gn, temp, 3, 1, -1, NAV_Data_Full_p->EARTH.gcc);
}
/******************************************************************************
*原  型：static void Lever(double* qnb, double* Mpv, double* pos,double *GnssArmLength, double* pos_)
*功  能： 初始化杆臂补偿，将天线相位中心的速度和位置归算至载体坐标系
*输  入： 
*输  出：无
*******************************************************************************/
void Lever(double* qnb, double* Mpv, double* pos,double *GnssArmLength, double* pos_)// 初始化杆臂补偿，将天线相位中心的速度和位置归算至载体坐标系
{
	double Cnb[3 * 3],MpvCnb[3 * 3];
	double dpos[3];

	Qnb2Cnb(qnb, Cnb);

	matmul("NN", 3, 3, 3, 1.0, Mpv, Cnb, 0.0, MpvCnb);//MpvCnb = Mpv*Cnb
	matmul("NN", 3, 1, 3, 1.0, MpvCnb, GnssArmLength, 0.0, dpos);//dpos=MpvCnb*GnssArmLength

	matrixSum(pos, dpos, 3, 1, -1, pos_);
}
/******************************************************************************
*原  型：void SINS_Init(SINS_t *sins)
*功  能：SINS姿态初始对准以及陀螺初始零偏计算
*输  入： 
*输  出：无
*******************************************************************************/
unsigned int StartCoarseAlign(_NAV_Data_Full_t* NAV_Data_Full_p)
{
	unsigned char flag = 0;
	int i = 0;
	static int cunt = 0;
	//double NavEuler[3];
	//double GnssPosition[3];
	static double imu_V_sum[3] = {0,0,0};
	static double imu_G_sum[3] = {0,0,0};
	static double imu_M_sum[3] = {0,0,0};
 double MA[3]={0.0,0.0,0.0};
	//读取定位位置
//	GnssPosition[0] =	gnss->latitude*DEG2RAD;
//	GnssPosition[1] = 	gnss->longitude * DEG2RAD;
//	GnssPosition[2] = 	0;//gnss->altitude ;//高程单位m

#if 0
	//求粗对准时候的四元素
	CoarseAlign(navins->imu.gyro, navins->imu.accm, GnssPosition, 1, navins->ins.qnb);
	//四元素转姿态
	qnb2att(navins->ins.qnb, navins->ins.att);
#endif
#if 0	
	navins->ins.att[0] = navins->imu.pitch*DEG2RAD;
	navins->ins.att[1] = navins->imu.roll*DEG2RAD;
	navins->ins.att[2] = navins->imu.heading*DEG2RAD;
#endif

	 
     for(i = 0;i<3;i++)
	 {
		 imu_V_sum[i]+= NAV_Data_Full_p->IMU.acc_use[i]/MAX_STATIC_STORED_COUNT;
		 imu_G_sum[i]+= NAV_Data_Full_p->IMU.gyro_use[i]/MAX_STATIC_STORED_COUNT; 	
		 imu_M_sum[i]+=	NAV_Data_Full_p->MAGNET.mag_use[i]/MAX_STATIC_STORED_COUNT;
	 }
#if 0
	 //加速度m/s2,初始校准加速度必须较小，原则上静态
    if(   fabs(NAV_Data_Full_p->IMU.acc_use[0]-NAV_Data_Full_p->IMU.acc_use_pre[0])>ALLIGN_DIFF_ACC_USE
		&&fabs(NAV_Data_Full_p->IMU.acc_use[1]-NAV_Data_Full_p->IMU.acc_use_pre[1])>ALLIGN_DIFF_ACC_USE
	    &&fabs(NAV_Data_Full_p->IMU.acc_use[2]-NAV_Data_Full_p->IMU.acc_use_pre[2])>ALLIGN_DIFF_ACC_USE
		&&fabs(NAV_Data_Full_p->IMU.gyro_use[2] < 1 * DEG2RAD)
	  )
	{
		cunt = 0;
		for(i = 0;i<3;i++)
		{
			imu_V_sum[i] = 0;
			imu_G_sum[i] = 0; 
			imu_M_sum[i] = 0;
		}
		
	}
#endif
	//航向初始化
	//采用磁力计时候
	//采用磁力计计算航向角
	if(		(E_USE_CASE_In_UAV == NAV_Data_Full_p->UseCase) 
		&&	(++cunt>= MAX_STATIC_STORED_COUNT) 
		&&0
		)
	{
		//受力投影
		//NAV_Data_Full_p->SINS.att[0] = atan2f( imu_V_sum[1],sqrt(imu_V_sum[0]*imu_V_sum[0]+imu_V_sum[2]*imu_V_sum[2]));//俯仰角
		//NAV_Data_Full_p->SINS.att[1] = atan2f(-imu_V_sum[0],imu_V_sum[2]);//横滚角

		NAV_Data_Full_p->SINS.att[0] = atan2( imu_V_sum[1],sqrt(imu_V_sum[0]*imu_V_sum[0]+imu_V_sum[2]*imu_V_sum[2]));//俯仰角
		NAV_Data_Full_p->SINS.att[1] = atan2(-imu_V_sum[0],imu_V_sum[2]);//横滚角

		NAV_Data_Full_p->SINS.att[2] = MagInitHeading(imu_M_sum,NAV_Data_Full_p->SINS.att,NAV_Data_Full_p->SINS.pos);
		att2qnb(NAV_Data_Full_p->SINS.att,NAV_Data_Full_p->SINS.qnb);	
		Qnb2Cnb(NAV_Data_Full_p->SINS.qnb,NAV_Data_Full_p->SINS.Cb2n);
		flag = 1;
		for(i = 0;i<3;i++)
		{
			//计算陀螺初始零偏
			NAV_Data_Full_p->SINS.gyro_off[i] = imu_G_sum[i];		 
		}
	}		
	else if(		(E_USE_CASE_In_Vehicle == NAV_Data_Full_p->UseCase)
				        &&	(NAV_Data_Full_p->GPS.gps_up_flag == E_GPS_IS_UPDATE)
	           &&((NAV_Data_Full_p->GPS.rtkStatus == E_GPS_RTK_FIXED)||(NAV_Data_Full_p->GPS.NO_RTK_heading_flag))//***ok！***
				        &&	(NAV_Data_Full_p->GPS.headingStatus == E_GPS_RTK_FIXED)
            &&(NAV_Data_Full_p->ins_buffer_full_flag) 
		       )
	{		
		
		//NAV_Data_Full_p->SINS.att[0] = atan2f( imu_V_sum[1],sqrt(imu_V_sum[0]*imu_V_sum[0]+imu_V_sum[2]*imu_V_sum[2]));//俯仰角
		//NAV_Data_Full_p->SINS.att[1] = atan2f(-imu_V_sum[0],imu_V_sum[2]);//横滚角
//		NAV_Data_Full_p->SINS.att[0] = atan2( imu_V_sum[1],sqrt(imu_V_sum[0]*imu_V_sum[0]+imu_V_sum[2]*imu_V_sum[2]));//俯仰角
//		NAV_Data_Full_p->SINS.att[1] = atan2(-imu_V_sum[0],imu_V_sum[2]);//横滚角
		//NAV_Data_Full_p->SINS.att[2] = CorrHeading((-NAV_Data_Full_p->GPS.Heading)+NAV_Data_Full_p->Param.gnssAtt_from_vehicle[2])*DEG2RAD;	//*****
		MA[0]=NAV_Data_Full_p->Macc[0]-NAV_Data_Full_p->SINS.db[0];
		MA[1]=NAV_Data_Full_p->Macc[1]-NAV_Data_Full_p->SINS.db[1];
		MA[2]=NAV_Data_Full_p->Macc[2]-NAV_Data_Full_p->SINS.db[2];
		
//		NAV_Data_Full_p->SINS.att[0] = atan2( NAV_Data_Full_p->Macc[1],sqrt(NAV_Data_Full_p->Macc[0]*NAV_Data_Full_p->Macc[0]+NAV_Data_Full_p->Macc[2]*NAV_Data_Full_p->Macc[2]));//俯仰角
//		NAV_Data_Full_p->SINS.att[1] = atan2(-NAV_Data_Full_p->Macc[0],NAV_Data_Full_p->Macc[2]);//横滚角
		NAV_Data_Full_p->SINS.att[0] = atan2( MA[1],sqrt(MA[0]*MA[0]+MA[2]*MA[2]));//俯仰角
		NAV_Data_Full_p->SINS.att[1] = atan2(-MA[0],MA[2]);//横滚角
		if(NAV_Data_Full_p->Nav_Standard_flag == E_NAV_STANDARD_PROCCSSED)
		{
			NAV_Data_Full_p->SINS.att[2] = DEG2RAD*CorrHeading(NAV_Data_Full_p->GPS.Heading_cor +NAV_Data_Full_p->Param.gnssAtt_from_vehicle2[2]);//航向
		}
		else
		{
			NAV_Data_Full_p->SINS.att[2] = DEG2RAD*NAV_Data_Full_p->GPS.Heading_cor;//航向
		}
		
		att2qnb(NAV_Data_Full_p->SINS.att,NAV_Data_Full_p->SINS.qnb);	
		Qnb2Cnb(NAV_Data_Full_p->SINS.qnb,NAV_Data_Full_p->SINS.Cb2n);
		Mat_Tr(3,NAV_Data_Full_p->SINS.Cb2n,NAV_Data_Full_p->SINS.Cn2b);
		
		NAV_Data_Full_p->SINS.pos[0]	=	NAV_Data_Full_p->GPS.Lat*DEG2RAD;//纬度
		NAV_Data_Full_p->SINS.pos[1]	=	NAV_Data_Full_p->GPS.Lon*DEG2RAD;//经度
		NAV_Data_Full_p->SINS.pos[2]	=	NAV_Data_Full_p->GPS.Altitude;//海拔高度 m 
		NAV_Data_Full_p->SINS.vn[0] = NAV_Data_Full_p->GPS.ve;
		NAV_Data_Full_p->SINS.vn[1] = NAV_Data_Full_p->GPS.vn;
		NAV_Data_Full_p->SINS.vn[2] = NAV_Data_Full_p->GPS.vu;	
		/*flag = 1;*/
		if (NAV_Data_Full_p->Nav_Standard_flag == E_NAV_STANDARD_PROCCSSED)
		{
			flag = 1;
		}
		else//*****标定阶段则需动态才能初始化****
		{
			if(sqrt(NAV_Data_Full_p->GPS.ve * NAV_Data_Full_p->GPS.ve + NAV_Data_Full_p->GPS.vn * NAV_Data_Full_p->GPS.vn) > 0.5)
				flag = 1;
			else
				flag = 0;
		}
		/*
		for(i = 0;i<3;i++)
		{
			//计算陀螺初始零偏
			NAV_Data_Full_p->SINS.gyro_off[i] = imu_G_sum[i];
			//NAV_Data_Full_p->Param.gyro_off[i] = NAV_Data_Full_p->Param.gyro_off[i] + NAV_Data_Full_p->SINS.gyro_off[i];
			NAV_Data_Full_p->Param.gyro_off[i] = NAV_Data_Full_p->SINS.gyro_off[i];
		}
		if(NAV_Data_Full_p->imuSelect == 1)
		NAV_Data_Full_p->Param.gyro_off[2] = NAV_Data_Full_p->Param.gyro_off[2] - sin(NAV_Data_Full_p->SINS.pos[0]) * 15 / 3600*DEG2RAD;
		*/
				
	}

	return flag;
}

/******************************************************************************
*原  型：void SINS_Init(SINS_t *sins)
*功  能：SINS初始对准
*输  入： 
*输  出：无
*******************************************************************************/
//理论上粗对准完成应该在GPS"整秒处"，此时给出的位置，航向才是准确的
void SINS_Init(_NAV_Data_Full_t* NAV_Data_Full_p)
{
	int i,j;
	int Align_flag = 0;//初始对准标识

	NAV_Data_Full_p->SINS.ts = (float)TS;
	NAV_Data_Full_p->SINS.nts = NAV_Data_Full_p->SINS.ts;
	//memset(NAV_Data_Full_p->SINS_buffer, 0, sizeof(NAV_Data_Full_p->SINS_buffer));
	//初始姿态对准
	
	
    //速度位置对准
	//if((NAV_Data_Full_p->debug &0x01) == 0x01 && 0)

//		NAV_Data_Full_p->SINS.pos[0]	=	NAV_Data_Full_p->GPS.Lat*DEG2RAD;//纬度
//		NAV_Data_Full_p->SINS.pos[1]	=	NAV_Data_Full_p->GPS.Lon*DEG2RAD;//经度
//		NAV_Data_Full_p->SINS.pos[2]	=	NAV_Data_Full_p->GPS.Altitude;//海拔高度 m 
//		NAV_Data_Full_p->SINS.vn[0] = NAV_Data_Full_p->GPS.ve;
//		NAV_Data_Full_p->SINS.vn[1] = NAV_Data_Full_p->GPS.vn;
//		NAV_Data_Full_p->SINS.vn[2] = NAV_Data_Full_p->GPS.vu;	
	
#if 0
	//SINS相关变量初始化
		for (i =0;i<3;i++)
		{		
			NAV_Data_Full_p->SINS.eb[i] = 0.0;
			NAV_Data_Full_p->SINS.db[i] = 0.0;
			NAV_Data_Full_p->SINS.an[i] = 0.0;
			NAV_Data_Full_p->SINS.fb_ib[i] = NAV_Data_Full_p->IMU.acc_use[i];
			NAV_Data_Full_p->SINS.fn[i] = 0.0;
			NAV_Data_Full_p->SINS.web[i] = NAV_Data_Full_p->IMU.gyro_use[i];
			NAV_Data_Full_p->SINS.wnb[i] = NAV_Data_Full_p->IMU.gyro_use[i];
//			NAV_Data_Full_p->SINS.an[i] = 0.0;
//			NAV_Data_Full_p->SINS.fb_ib[i] = 0.0;
//			NAV_Data_Full_p->SINS.fn[i] = 0.0;
//			NAV_Data_Full_p->SINS.web[i] = 0.0;
//			NAV_Data_Full_p->SINS.wnb[i] = 0.0;
		}
		NAV_Data_Full_p->SINS.fn[2] = G0;
#endif
		//if(NAV_Data_Full_p->Nav_Standard_flag == E_NAV_STANDARD_PROCCSSED)
		//{
			 //for (i =0;i<3;i++)
			 // {
				//NAV_Data_Full_p->SINS.eb[i] = NAV_Data_Full_p->Param.gyro_off[i];
			   // NAV_Data_Full_p->SINS.db[i] = NAV_Data_Full_p->Param.acc_off[i];
			 // }
					
		//}
		//else
		//{
			//for (i = 0; i < 3; i++)
			//{
				//NAV_Data_Full_p->SINS.eb[i] = 0.0;
				//NAV_Data_Full_p->SINS.db[i] = 0.0;
			//}
	//	}
		//NAV_Data_Full_p->SINS.eb[2] = NAV_Data_Full_p->SINS.eb[2] - sin(NAV_Data_Full_p->SINS.pos[0]) * 15 / 3600;
		
	 NAV_Data_Full_p->UseCase = E_USE_CASE_In_Vehicle;//使用场景：0：默认车载使用六轴+GNSS+ODS       1:九轴无人机
	 Align_flag = StartCoarseAlign(NAV_Data_Full_p);//*****初始化INS*****
		
	//对准状态
	if(Align_flag == 1)
	{
		NAV_Data_Full_p->SINS.Init_flag = 1;
		//初始化SINS相关变量
		for (i = 0; i < 3; i++)
		{
			NAV_Data_Full_p->SINS.an[i] = 0.0;
			NAV_Data_Full_p->SINS.fb_ib[i] = NAV_Data_Full_p->IMU.acc_use[i]- NAV_Data_Full_p->SINS.db[i];
			NAV_Data_Full_p->SINS.web[i] = NAV_Data_Full_p->IMU.gyro_use[i];
			NAV_Data_Full_p->SINS.wnb[i] = NAV_Data_Full_p->IMU.gyro_use[i];
		}
		matmul("NN", 3, 1, 3, 1, NAV_Data_Full_p->SINS.Cb2n, NAV_Data_Full_p->SINS.fb_ib, 0.0, NAV_Data_Full_p->SINS.fn);
		//******outage仿真计时开始****
		//NAV_Data_Full_p->outage_start = NAV_Data_Full_p->GPS.gpssecond982 / 1000.0;
		//**确保采集的数据足够长满足需要*******
		//for (i = 0; i < outnum; i++)
		//{
			//NAV_Data_Full_p->outage_start_per[i]= (8.0 * 60 + NAV_Data_Full_p->GPS.gpssecond982/1000.0) + i * (outagetime + spantime);
		//}
		//*******************************
			//地球模型初始化
	Earth_Init(NAV_Data_Full_p);
	Earth_UP(NAV_Data_Full_p,NAV_Data_Full_p->SINS.pos, NAV_Data_Full_p->SINS.vn);
	NAV_Data_Full_p->Car_SOURCE_UP_Cur_ms = NAV_Data_Full_p->GPS.gpssecond;
	//Mpv矩阵初始化
	for (i = 0; i < 3; i++)
	{
		for (j = 0; j < 3; j++)
		{
			NAV_Data_Full_p->SINS.Mpv[j + i * 3] = 0.0;
		}
	}
	NAV_Data_Full_p->SINS.Mpv[1] = 1 / NAV_Data_Full_p->EARTH.clRNh; 
	NAV_Data_Full_p->SINS.Mpv[3] = 1 / NAV_Data_Full_p->EARTH.RMh; 
	NAV_Data_Full_p->SINS.Mpv[8] = 1.0;
#if 0
	// 初始化杆臂补偿，将天线相位中心的速度和位置归算至载体坐标系******初始化时候不用补偿杆臂参数********
	Lever(NAV_Data_Full_p->SINS.qnb,
		NAV_Data_Full_p->SINS.Mpv,
		NAV_Data_Full_p->SINS.pos,
		NAV_Data_Full_p->Param.gnssArmLength, 
		NAV_Data_Full_p->SINS.pos);
#endif	
	}
	else
	{
		NAV_Data_Full_p->SINS.Init_flag = 0;
	}	
}

//一子样函数
void Cnscl_1(_NAV_Data_Full_t* NAV_Data_Full_p,double* gyro, double* acc)
{
	int i;
	//double cm[3], sm[3];
	//double phim_[3];
	double wmm_vmm[3];
	//double cm_pvm[3];
	//double sm_pwm[3];
	//double temp1[3];

#if 0
	//单子样，划桨补偿量为0
	for (i = 0; i < 3; i++)
	{
		gyro[i]=gyro[i];
	}
#endif	
	//速度旋转补偿量
	cross3(gyro, acc, wmm_vmm);	
	for (i = 0; i < 3; i++)
	{
		acc[i] = acc[i]+0.5 * wmm_vmm[i];
	}

	//此处可以采用"单子样+前一周算法"参考8.1.2，未完成
#if 0
	for (i = 0; i < 3; i++) {
		cm[i] = (g_gyro_cal[i]) / 12.0;
		sm[i] = (g_acc_cal[i]) / 12.0;
	}
	//采用"单子样+前一周算法"
	memset(temp1,0,sizeof(temp1));
	cross3(cm, gyro, temp1);
	for (i = 0; i < 3; i++)
	{
		gyro[i]=gyro[i]+temp1[i];
	}
	
	memset(temp1,0,sizeof(temp1));
	memset(temp2,0,sizeof(temp2));
	cross3(cm, acc, temp1);
	cross3(sm, gyro, temp2);
	for (i = 0; i < 3; i++)
	{
		acc[i] = acc[i]+temp1[i]+temp2[i];
	}
#endif
	
}
#if 1

//求vn1
void SINS_UP_VEL(_NAV_Data_Full_t* NAV_Data_Full_p, double *vn1,double *dvbm)
{
	int i;
	double nts = NAV_Data_Full_p->SINS.nts;
	double rv[3],m[3*3];
	double qn_dvbm[3],m_qn_dvbm[3];
	double dvgcc[3];
	//rv_m=-wnin*nts/2
	for (i = 0; i < 3; i++) 
	{
		rv[i]=NAV_Data_Full_p->EARTH.wnin[i]*(-nts/2.0);
		//计算有害速度增量
		dvgcc[i]=NAV_Data_Full_p->EARTH.gcc[i]*nts;
	}
	rv2m(rv,m);
	qmulv(NAV_Data_Full_p->SINS.qnb,dvbm,qn_dvbm);
	//m函数趋于单位矩阵
	matmul("NN", 3, 1, 3, 1.0, m, qn_dvbm, 0.0, m_qn_dvbm);
	//补偿比力速度增量
	matrixSum(NAV_Data_Full_p->SINS.vn, m_qn_dvbm, 3, 1, 1, vn1);
	//补偿有害加速度
	matrixSum(vn1, dvgcc, 3, 1, 1, vn1);
}

void SINS_UP_ATT(_NAV_Data_Full_t* NAV_Data_Full_p,double *phim)
{
	int i;
	double nts = NAV_Data_Full_p->SINS.nts;
	double rvn[3]={0.0};
	double rvn_q[4]={0.0};
	double rvb_q[4]={0.0};
	double rvn_m[3*3]={0.0};
	double rvb_m[3*3]={0.0};
	double sincb2n[3*3]={0.0};
	double normqnb=0.0;
	double sinsqnb[4]={0.0};
	//导航系旋转
	for (i = 0; i < 3; i++) 
	{
		rvn[i]=(-NAV_Data_Full_p->EARTH.wnin[i])*nts;
	}
#if 1	
	rv2q(rvn, rvn_q);	
	//b系旋转
	rv2q(phim, rvb_q);
	qnbmul(NAV_Data_Full_p->SINS.qnb, rvb_q,sinsqnb);
	qnbmul(rvn_q,sinsqnb,sinsqnb);
	//单位化
	normqnb=norm(sinsqnb, 4);
	if (normqnb <=0.0) 
	{
#ifdef WIN32 
		inav_log(INAVMD(LOG_ERR),"sinsqnb=%f",sinsqnb);
#endif
		return;
	}
	else
	{
		for (i = 0; i < 4; i++)
		{
			NAV_Data_Full_p->SINS.qnb[i]=sinsqnb[i]/normqnb;
		}
	}
	qnb2att(NAV_Data_Full_p->SINS.qnb, NAV_Data_Full_p->SINS.att);
	Qnb2Cnb(NAV_Data_Full_p->SINS.qnb, NAV_Data_Full_p->SINS.Cb2n);
	Mat_Tr(3,NAV_Data_Full_p->SINS.Cb2n,NAV_Data_Full_p->SINS.Cn2b);
#endif

#if 0
	rv2m(rvn, rvn_m);
	rv2m(phim, rvb_m);
	matmul("NN",3,3,3,1,NAV_Data_Full_p->SINS.Cb2n,rvb_m,0,sincb2n);
	matmul("TN",3,3,3,1,rvn_m,sincb2n,0,NAV_Data_Full_p->SINS.Cb2n);
	Mat_Tr(3,NAV_Data_Full_p->SINS.Cb2n,NAV_Data_Full_p->SINS.Cn2b);
	Cnb2att(NAV_Data_Full_p->SINS.Cb2n,NAV_Data_Full_p->SINS.att);
	att2qnb(NAV_Data_Full_p->SINS.att,NAV_Data_Full_p->SINS.qnb);
#endif	
	
}

double g_gyro_filter2=0.0;
double P_gyro_factor=1000.0*1000.0;
double Q_gyro_factor=0.03*0.03;//0.02*0.02
double R_gyro_factor=2.0*2.0;
//double Diff_Gn_ACC=0.0;

/******************************************************************************
*原  型：void SINS_Up(IMUDATA *imu,INSRESULT *ins,I_NAV_INS * navins) 
*功  能：SINS更新(姿态更新算法为四阶近似的毕卡迭代算法)
*输  入：imu数据，nav数据
*输  出：无
*******************************************************************************/
void SINS_UP_HP(_NAV_Data_Full_t* NAV_Data_Full_p)
{
	int i;
	double nts = NAV_Data_Full_p->SINS.nts; 
	double nts_2 = nts*nts;
	double wb_set[3] = {0*DEG2RAD,0*DEG2RAD,0*DEG2RAD};
    double db_set[3] = {0,0,0};
	double gyro[3], acc[3];
	double phim[3],dvbm[3];
	double vn1[3];


	if(1)
	{
		//IMU处理
		gyro[0] = NAV_Data_Full_p->IMU.gyro_use[0] + wb_set[0];
		gyro[1] = NAV_Data_Full_p->IMU.gyro_use[1] + wb_set[1];
		gyro[2] = NAV_Data_Full_p->IMU.gyro_use[2] + wb_set[2];
		acc[0] = NAV_Data_Full_p->IMU.acc_use[0] + db_set[0];
		acc[1] = NAV_Data_Full_p->IMU.acc_use[1] + db_set[1];
		acc[2] = NAV_Data_Full_p->IMU.acc_use[2] + db_set[2];
	}
	
	//扣除估计的零偏 
	for (i = 0; i < 3; i++) 
	{
		gyro[i] = gyro[i] - NAV_Data_Full_p->SINS.eb[i];   //rad/s
		acc[i] = acc[i] - NAV_Data_Full_p->SINS.db[i];  //m/s^2	
	}

#if 0
	//对gyro[2]滤波
	Scalar_KalmanFilte(&g_gyro_filter2,gyro[2],&P_gyro_factor,Q_gyro_factor,R_gyro_factor,20*DEG2RAD);
	gyro[2]=g_gyro_filter2;
#endif	


/**********************************************************************************/
//------------------------------------------------------------------------------------
//单子样 圆锥/划船误差补充，参考8.2.1,计算旋转角度更新,
	Cnscl_1(NAV_Data_Full_p,gyro,acc);
	
//计算i系下角度增量及速度增量
	for (i = 0; i < 3; i++) 
	{
		phim[i]	=	gyro[i]*nts;//rad
		dvbm[i]	=	acc[i]*nts;	//m/s
		//phim[i]=0.5*(gyro[i]+NAV_Data_Full_p->SINS.wb_ib[i])*nts;
		//dvbm[i]=0.5*(acc[i]+NAV_Data_Full_p->SINS.fb_ib[i])*nts;
	}
	
	for (i = 0; i < 3; i++) 
	{
		NAV_Data_Full_p->SINS.wb_ib[i] = phim[i]/nts;
		NAV_Data_Full_p->SINS.fb_ib[i] = dvbm[i]/nts;
	}
//earth & angular rate updating 
	//使用中间位置速度进行地球更新
	double vn01[3],pos01[3];
	for (i = 0; i < 3; i++) 
	{
		vn01[i]=NAV_Data_Full_p->SINS.vn[i]+NAV_Data_Full_p->SINS.an[i]*(nts / 2);
	}
	pos01[0]	=	NAV_Data_Full_p->SINS.pos[0] + \
										(NAV_Data_Full_p->SINS.vn[1]+vn01[1])*(nts / 2)/NAV_Data_Full_p->EARTH.RMh;
	pos01[1]	=	NAV_Data_Full_p->SINS.pos[1] + \
										(NAV_Data_Full_p->SINS.vn[0]+vn01[0])*(nts / 2)/NAV_Data_Full_p->EARTH.clRNh;
	pos01[2]	=	NAV_Data_Full_p->SINS.pos[2] + \
										(NAV_Data_Full_p->SINS.vn[2]+vn01[2])*(nts / 2);
	Earth_UP(NAV_Data_Full_p,pos01,vn01);


#if 1
	//测试重力加速度与接收加计数据对比
	//Diff_Gn_ACC=fabs(norm(NAV_Data_Full_p->IMU.acc_use,3)-norm(NAV_Data_Full_p->EARTH.gn,3));
#endif

	
//更新大地坐标系
	//Earth_UP(NAV_Data_Full_p,NAV_Data_Full_p->SINS.pos,NAV_Data_Full_p->SINS.vn);
//更新速度
    SINS_UP_VEL(NAV_Data_Full_p,vn1,dvbm);
//用平均速度进行位置更新
//---------------pos update---------------------------------------------------------------
	
	NAV_Data_Full_p->SINS.pos[0]	=	NAV_Data_Full_p->SINS.pos[0] + \
										(NAV_Data_Full_p->SINS.vn[1]+vn1[1])*(nts / 2)/NAV_Data_Full_p->EARTH.RMh;
	NAV_Data_Full_p->SINS.pos[1]	=	NAV_Data_Full_p->SINS.pos[1] + \
										(NAV_Data_Full_p->SINS.vn[0]+vn1[0])*(nts / 2)/NAV_Data_Full_p->EARTH.clRNh;
	NAV_Data_Full_p->SINS.pos[2]	=	NAV_Data_Full_p->SINS.pos[2] + \
										(NAV_Data_Full_p->SINS.vn[2]+vn1[2])*(nts / 2);

//---------------vel update---------------------------------------------------------------	
	NAV_Data_Full_p->SINS.vn[0]		=	vn1[0];
	NAV_Data_Full_p->SINS.vn[1]		=	vn1[1];
	NAV_Data_Full_p->SINS.vn[2]		=	vn1[2];

//---------------att update---------------------------------------------------------------
	SINS_UP_ATT(NAV_Data_Full_p,phim);


//---------------sins update for kalman---------------------------------------------------------------
	double wie_b[3],win_b[3];
	//计算载体坐标系相对大地坐标的角速度补偿
	matmul("TN", 3, 1, 3, 1, NAV_Data_Full_p->SINS.Cb2n, NAV_Data_Full_p->EARTH.wnie,0.0,wie_b);
   //计算载体坐标系相对导航坐标系的角速度补偿
	matmul("TN", 3, 1, 3, 1, NAV_Data_Full_p->SINS.Cb2n, NAV_Data_Full_p->EARTH.wnin, 0.0, win_b);
   //计算导航坐标系下的加速度，尚未补偿重力加速度
	matmul("NN", 3, 1, 3, 1, NAV_Data_Full_p->SINS.Cb2n, NAV_Data_Full_p->SINS.fb_ib,0.0,NAV_Data_Full_p->SINS.fn);
	//载体相对导航系的加速度计算
	for(i = 0;i<3;i++)
	{	
		//计算大地坐标系下的角速度
		NAV_Data_Full_p->SINS.web[i] = NAV_Data_Full_p->SINS.wb_ib[i]- wie_b[i];
		//计算导航系下的角速度
		NAV_Data_Full_p->SINS.wnb_pre[i] = NAV_Data_Full_p->SINS.wnb[i];
		NAV_Data_Full_p->SINS.wnb[i] = NAV_Data_Full_p->SINS.wb_ib[i]- win_b[i];
		//计算导航系下的加速度，补偿上重力加速度m/s2,举例{-4.0409829786878522e-07, 2.9398186206018674e-07, -9.7879837185144272}
		NAV_Data_Full_p->SINS.an[i] = NAV_Data_Full_p->SINS.fn[i] + NAV_Data_Full_p->EARTH.gcc[i];
	}
	
//**********************************************************************************************	
	//再次更新大地坐标
	//Earth_UP(NAV_Data_Full_p,NAV_Data_Full_p->SINS.pos,NAV_Data_Full_p->SINS.vn);
	
//杆臂补偿要用到的值
	NAV_Data_Full_p->SINS.Mpv[1] = 1 / NAV_Data_Full_p->EARTH.clRNh; 
	NAV_Data_Full_p->SINS.Mpv[3] = 1 / NAV_Data_Full_p->EARTH.RMh; 
	NAV_Data_Full_p->SINS.Mpv[8] = 1.0; 	

	
    //数据存buffer
	{
		//qnb
		for(i = 0;i<4;i++)
		{
			NAV_Data_Full_p->SINS_buffer[NAV_Data_Full_p->Head].qnb[i] = NAV_Data_Full_p->SINS.qnb[i];
		}
		//att、vn、pos
		for(i = 0;i<3;i++)
		{
			NAV_Data_Full_p->SINS_buffer[NAV_Data_Full_p->Head].att[i] = NAV_Data_Full_p->SINS.att[i];
			NAV_Data_Full_p->SINS_buffer[NAV_Data_Full_p->Head].vn[i] = NAV_Data_Full_p->SINS.vn[i];
			NAV_Data_Full_p->SINS_buffer[NAV_Data_Full_p->Head].pos[i] = NAV_Data_Full_p->SINS.pos[i];			
		}
		//wnb
		for(i = 0;i<3;i++)
		{
			NAV_Data_Full_p->SINS_buffer[NAV_Data_Full_p->Head].wnb[i] = NAV_Data_Full_p->SINS.wnb[i];			
		}        		
        //Mpv
		for(i = 0;i<9;i++)
		{
			NAV_Data_Full_p->SINS_buffer[NAV_Data_Full_p->Head].Mpv[i] = NAV_Data_Full_p->SINS.Mpv[i];
		}
		//an
		for(i = 0;i<3;i++)
		{
			NAV_Data_Full_p->SINS_buffer[NAV_Data_Full_p->Head].an[i] = NAV_Data_Full_p->SINS.an[i];			
		}
		//平均轮速
		NAV_Data_Full_p->SINS_buffer[NAV_Data_Full_p->Head].WheelSpeed_ave = NAV_Data_Full_p->ODS.WheelSpeed_ave;
		if(++NAV_Data_Full_p->Head>=SINS_BUFFER_SIZE)
		{
			NAV_Data_Full_p->Head = 0;
		}
	}
}
#endif
/******************************************************************************
*原  型：void SINS_Up(IMUDATA *imu,INSRESULT *ins,I_NAV_INS * navins) 
*功  能：SINS更新(姿态更新算法为四阶近似的毕卡迭代算法)
*输  入：imu数据，nav数据
*输  出：无
*******************************************************************************/
//double att_temp[3] = {0};
void SINS_UP(_NAV_Data_Full_t* NAV_Data_Full_p)
{
	int i;
	double nts = NAV_Data_Full_p->SINS.nts; //0.005
	double nts_2 = nts*nts;
	double gyro[3], acc[3];
	double wie_b[3],win_b[3];
	//double Cnb[3 * 3];
	double wb_set[3] = {0*DEG2RAD,0*DEG2RAD,0*DEG2RAD};
    double db_set[3] = {0,0,0};

	//姿态更新中间变量
    double theta_2 = 0;
	double temp = 0;	
	double temp_V4[4] = {0,0,0,0};
//------------------------------------------------------------------------------------
//数据准备	
//    //人为添加零偏以及扣除初始零偏
	if(1)
	{
		//IMU数据处理，先不管
		if(0)
		{
//        //NAV_USE_MEMS+FOG
//		if(E_IMU_ATT_IFOG==NAV_Data_Full_p->imuSelect)
//		{
////			gyro[0] = NAV_Data_Full_p->IMU.gyro_use[0] - NAV_Data_Full_p->SINS.gyro_off[0] + wb_set[0];
////			gyro[1] = NAV_Data_Full_p->IMU.gyro_use[1] - NAV_Data_Full_p->SINS.gyro_off[1] + wb_set[1];
////			gyro[2] = NAV_Data_Full_p->IMU.gyro_use[2]                                     + wb_set[2];
//			gyro[0] = NAV_Data_Full_p->IMU.gyro_use[0] + wb_set[0];
//			gyro[1] = NAV_Data_Full_p->IMU.gyro_use[1] + wb_set[1];
//			gyro[2] = NAV_Data_Full_p->IMU.gyro_use[2] + wb_set[2];
//			
//			acc[0] = NAV_Data_Full_p->IMU.acc_use[0] - NAV_Data_Full_p->SINS.acc_off[0] + db_set[0];
//			acc[1] = NAV_Data_Full_p->IMU.acc_use[1] - NAV_Data_Full_p->SINS.acc_off[1] + db_set[1];
//			acc[2] = NAV_Data_Full_p->IMU.acc_use[2] - NAV_Data_Full_p->SINS.acc_off[2] + db_set[2];

//		}
//		//NAV_USE_MEMS
//		else
//		{
////			gyro[0] = NAV_Data_Full_p->IMU.gyro_use[0] - NAV_Data_Full_p->SINS.gyro_off[0] + wb_set[0];
////			gyro[1] = NAV_Data_Full_p->IMU.gyro_use[1] - NAV_Data_Full_p->SINS.gyro_off[1] + wb_set[1];
////			gyro[2] = NAV_Data_Full_p->IMU.gyro_use[2] - NAV_Data_Full_p->SINS.gyro_off[2] + wb_set[2];

//			gyro[0] = NAV_Data_Full_p->IMU.gyro_use[0] + wb_set[0];
//			gyro[1] = NAV_Data_Full_p->IMU.gyro_use[1] + wb_set[1];
//			gyro[2] = NAV_Data_Full_p->IMU.gyro_use[2] + wb_set[2];
//			
//			acc[0] = NAV_Data_Full_p->IMU.acc_use[0] - NAV_Data_Full_p->SINS.acc_off[0] + db_set[0];
//			acc[1] = NAV_Data_Full_p->IMU.acc_use[1] - NAV_Data_Full_p->SINS.acc_off[1] + db_set[1];
//			acc[2] = NAV_Data_Full_p->IMU.acc_use[2] - NAV_Data_Full_p->SINS.acc_off[2] + db_set[2];
//		}
		}
		
		//IMU处理
		gyro[0] = NAV_Data_Full_p->IMU.gyro_use[0] + wb_set[0];
		gyro[1] = NAV_Data_Full_p->IMU.gyro_use[1] + wb_set[1];
		gyro[2] = NAV_Data_Full_p->IMU.gyro_use[2] + wb_set[2];
		acc[0] = NAV_Data_Full_p->IMU.acc_use[0] + db_set[0];
		acc[1] = NAV_Data_Full_p->IMU.acc_use[1] + db_set[1];
		acc[2] = NAV_Data_Full_p->IMU.acc_use[2] + db_set[2];			
	}
    //扣除估计的零偏 
	for (i = 0; i < 3; i++) 
	{
		gyro[i] = gyro[i] -NAV_Data_Full_p->SINS.eb[i];   //rad/s
		acc[i] = acc[i] - NAV_Data_Full_p->SINS.db[i];  //m/s^2	
	}
//------------------------------------------------------------------------------------
	for (i = 0; i < 3; i++)
	{
		NAV_Data_Full_p->SINS.wb_ib[i] = gyro[i];
		NAV_Data_Full_p->SINS.fb_ib[i] = acc[i];
	}
	Earth_UP(NAV_Data_Full_p,NAV_Data_Full_p->SINS.pos, NAV_Data_Full_p->SINS.vn);//更新大地数据
//	Qnb2Cnb(NAV_Data_Full_p->SINS.qnb, Cnb);
	//计算载体坐标系相对大地坐标的角速度补偿
	matmul("TN", 3, 1, 3, 1, NAV_Data_Full_p->SINS.Cb2n, NAV_Data_Full_p->EARTH.wnie,0.0,wie_b);
   //计算载体坐标系相对导航坐标系的角速度补偿
	matmul("TN", 3, 1, 3, 1, NAV_Data_Full_p->SINS.Cb2n, NAV_Data_Full_p->EARTH.wnin, 0.0, win_b);
   //计算导航坐标系下的加速度，尚未补偿重力加速度
	matmul("NN", 3, 1, 3, 1, NAV_Data_Full_p->SINS.Cb2n, NAV_Data_Full_p->SINS.fb_ib,0.0,NAV_Data_Full_p->SINS.fn);
	//载体相对导航系的加速度计算
	static double wnb_pre[3] = {0};
    for(i = 0;i<3;i++)
	{   
	    //计算大地坐标系下的角速度
		NAV_Data_Full_p->SINS.web[i] = NAV_Data_Full_p->SINS.wb_ib[i]- wie_b[i];
		//计算导航系下的角速度
		NAV_Data_Full_p->SINS.wnb_pre[i] = NAV_Data_Full_p->SINS.wnb[i];
		wnb_pre[i] = NAV_Data_Full_p->SINS.wnb[i];
		NAV_Data_Full_p->SINS.wnb[i] = NAV_Data_Full_p->SINS.wb_ib[i];
		if (NAV_Data_Full_p->imuSelect == 1)
		NAV_Data_Full_p->SINS.wnb[i] = NAV_Data_Full_p->SINS.wb_ib[i]- win_b[i];
		//计算导航系下的加速度，补偿上重力加速度m/s2,举例{-4.0409829786878522e-07, 2.9398186206018674e-07, -9.7879837185144272}
		//
		NAV_Data_Full_p->SINS.an[i] = NAV_Data_Full_p->SINS.fn[i] + NAV_Data_Full_p->EARTH.gcc[i];
	}

/////////////////////////////////////////////////////////////////////////////////	
	
	
	if(NAV_Data_Full_p->ODS.Gear == 0)
	{
		//for (int i =0; i<3;i++)
    {
		//NAV_Data_Full_p->SINS.pos[i] = pos_pre[i];
	}
	}
	
	
	

	
	
	
////////////////////////////////////////////////////////////////////////////////////	
//速度更新	v1 = v0+a*dt	
	double rot_error[3] = {0};
	cross3(NAV_Data_Full_p->SINS.wnb, NAV_Data_Full_p->SINS.an, rot_error);//旋转误差补偿
	for(i = 0;i<3;i++)
	{
	  NAV_Data_Full_p->SINS.an[i] = NAV_Data_Full_p->SINS.an[i] + rot_error[i] /2 * nts;//**********************************************??
	}
	
	for(i = 0;i<3;i++)
	{
		NAV_Data_Full_p->SINS.vn[i] = NAV_Data_Full_p->SINS.vn[i] + NAV_Data_Full_p->SINS.an[i]*nts;
	}

	//判断载体静止速度姿态停止更新
	//static int count_static = 0;
	//if (fabs(gyro[0]) * RAD2DEG < 0.1
		//&& fabs(gyro[1]) * RAD2DEG < 0.1
		//&& fabs(gyro[2]) * RAD2DEG < 0.1
		//&& fabs(NAV_Data_Full_p->IMU.acc_use[0] - NAV_Data_Full_p->IMU.acc_use_pre[0]) < 0.2
		//&& fabs(NAV_Data_Full_p->IMU.acc_use[1] - NAV_Data_Full_p->IMU.acc_use_pre[1]) < 0.2
		//&& fabs(NAV_Data_Full_p->IMU.acc_use[2] - NAV_Data_Full_p->IMU.acc_use_pre[2]) < 0.2
		//&& fabs(NAV_Data_Full_p->SINS.vn[1]) < 0.1
		//&&(NAV_Data_Full_p->ODS.WheelSpeed_Back_Right)< 0.1
		//&&(NAV_Data_Full_p->ODS.WheelSpeed_Front_Right)< 0.1
		//&&NAV_Data_Full_p->ODS.ods_flag == 1
		//)
	{
		//count_static++;
		//if (count_static > 20)
		{
			//NAV_Data_Full_p->SINS.wnb[0] = 0;
			//NAV_Data_Full_p->SINS.wnb[1] = 0;
			//NAV_Data_Full_p->SINS.wnb[2] = 0;
			//NAV_Data_Full_p->SINS.an[0] =0;
			//NAV_Data_Full_p->SINS.an[1] =0;
			//NAV_Data_Full_p->SINS.an[2] =0;
			//NAV_Data_Full_p->SINS.vn[0] = 0;
			//NAV_Data_Full_p->SINS.vn[1] = 0;
			//NAV_Data_Full_p->SINS.vn[2] = 0;
			//printf("11\n");
		}
	}
	//else
	{
		//printf("22\n");
		//count_static = 0;
	}
	 //位置更新  p1 = p0+(v0+0.5*a*dt)*dt          //lock position ,the result is same
	//double pos_pre[3] = {0};
	 //for (int i =0; i<3;i++)
	// {
	// pos_pre[i] = NAV_Data_Full_p->SINS.pos[i];
	// }
	NAV_Data_Full_p->SINS.pos_pre[0] = NAV_Data_Full_p->SINS.pos[0];
	NAV_Data_Full_p->SINS.pos_pre[1] = NAV_Data_Full_p->SINS.pos[1];
	NAV_Data_Full_p->SINS.pos_pre[2] = NAV_Data_Full_p->SINS.pos[2];
	NAV_Data_Full_p->SINS.pos[0] = NAV_Data_Full_p->SINS.pos[0] + (NAV_Data_Full_p->SINS.vn[1] + 0.5 * NAV_Data_Full_p->SINS.an[1] * nts) / NAV_Data_Full_p->EARTH.RMh * nts;
	 NAV_Data_Full_p->SINS.pos[1] = NAV_Data_Full_p->SINS.pos[1] + (NAV_Data_Full_p->SINS.vn[0]+0.5*NAV_Data_Full_p->SINS.an[0]*nts)/NAV_Data_Full_p->EARTH.clRNh * nts;
	 NAV_Data_Full_p->SINS.pos[2] = NAV_Data_Full_p->SINS.pos[2] + (NAV_Data_Full_p->SINS.vn[2]+0.5*NAV_Data_Full_p->SINS.an[2]*nts) * nts;
	
if(0)
{
	if(NAV_Data_Full_p->ODS.Gear == 0)
	////{if(NAV_Data_Full_p->SINS.vn[i]== 0)
	for(i = 0;i<3;i++)
		{
			//NAV_Data_Full_p->SINS.wnb[i] = 0;
		}
}
	
	//单子样加前一周期
	double phi[3] = {0};
	
	cross3(wnb_pre, NAV_Data_Full_p->SINS.wnb, phi);
	for(i = 0;i<3;i++)
	{
		phi[i] =  NAV_Data_Full_p->SINS.wnb[i]*nts + phi[i]/12 *nts*nts;
	}
	

	if (NAV_Data_Full_p->ODS.WheelSpeed_Back_Right == 0
		&& NAV_Data_Full_p->ODS.WheelSpeed_Front_Right == 0
		&& E_FUNSION_WHEEL == NAV_Data_Full_p->KF.fusion_source
		&& norm(NAV_Data_Full_p->SINS.wnb, 3) < 1 * DEG2RAD)
	{
		/*if (fabs(NAV_Data_Full_p->SINS.wnb[2]) < 0.5 * DEG2RAD)
		{
			Gcount++;
			Gsum = Gsum + NAV_Data_Full_p->SINS.wnb[2];
		}
		else
		{
			Gcount = 0;
			Gsum = 0;
		}*/
		for (i = 0; i < 3; i++)
		{
			NAV_Data_Full_p->SINS.wnb[i] = 0.1 * NAV_Data_Full_p->SINS.wnb[i];
			//NAV_Data_Full_p->SINS.vn[i] = 0;
		}
		/*if (Gcount > 3000)
		{
			NAV_Data_Full_p->SINS.eb[2] = NAV_Data_Full_p->SINS.eb[2] + Gsum / Gcount;
			Gsum = 0;
			Gcount = 0;
		}*/
	}
	
	//if (sqrt(NAV_Data_Full_p->GPS.vn * NAV_Data_Full_p->GPS.vn + NAV_Data_Full_p->GPS.ve * NAV_Data_Full_p->GPS.ve) <0.01
	//	&& E_FUNSION_GPS == NAV_Data_Full_p->KF.fusion_source
	//	&& norm(NAV_Data_Full_p->SINS.wnb,3) < 2* DEG2RAD)
	//{
	//	/*if (fabs(NAV_Data_Full_p->SINS.wnb[2]) < 0.5 * DEG2RAD)
	//	{
	//		Gcount++;
	//		Gsum = Gsum + NAV_Data_Full_p->SINS.wnb[2];
	//	}
	//	else
	//	{
	//		Gcount = 0;
	//		Gsum = 0;
	//	}*/
	//	for (i = 0; i < 3; i++)
	//	{
	//		NAV_Data_Full_p->SINS.wnb[i] = 0;
	//		NAV_Data_Full_p->SINS.vn[i] = 0;
	//	}
	//	/*if (Gcount > 3000)
	//	{
	//		NAV_Data_Full_p->SINS.eb[2] = NAV_Data_Full_p->SINS.eb[2] + Gsum / Gcount;
	//		Gsum = 0;
	//		Gcount = 0;
	//	}*/
	//}

	//NAV_Data_Full_p->SINS.wnb[2] = NAV_Data_Full_p->SINS.wnb[2] / (1- NAV_Data_Full_p->SINS.es);
///////////////////////////////////////////////////////////////////////////////////
	//姿态更新四阶近似的毕卡迭代法      REVISED THIS ALGORITHM
	{
			
			{
					for(i = 0;i<3;i++)
					{	
						if(1)
							NAV_Data_Full_p->SINS.dtheta[i] = NAV_Data_Full_p->SINS.wnb[i]*nts;
						else
							NAV_Data_Full_p->SINS.dtheta[i] = phi[i];
					}
					//
					{
							temp = 0;
							for(i = 0;i<3;i++)
							{
									temp += NAV_Data_Full_p->SINS.dtheta[i]*NAV_Data_Full_p->SINS.dtheta[i];
							}
					}
					theta_2 = temp;
					
					temp = 0.5-0.02083*theta_2;  
					for(i = 0;i<4;i++)
					{
							if(i == 0)
							{
									temp_V4[0] = 1-0.125*theta_2+0.002604*theta_2*theta_2;
							}
							else
							{
									temp_V4[i] = NAV_Data_Full_p->SINS.dtheta[i-1]*temp;
							}
					}
			}
			//Q_up
			{
					//q_k1 = M'*q_k
					NAV_Data_Full_p->SINS.qnb[0] = temp_V4[0]*NAV_Data_Full_p->SINS.qnb[0] - temp_V4[1]*NAV_Data_Full_p->SINS.qnb[1] - temp_V4[2]*NAV_Data_Full_p->SINS.qnb[2] - temp_V4[3]*NAV_Data_Full_p->SINS.qnb[3];
					NAV_Data_Full_p->SINS.qnb[1] = temp_V4[1]*NAV_Data_Full_p->SINS.qnb[0] + temp_V4[0]*NAV_Data_Full_p->SINS.qnb[1] + temp_V4[3]*NAV_Data_Full_p->SINS.qnb[2] - temp_V4[2]*NAV_Data_Full_p->SINS.qnb[3];
					NAV_Data_Full_p->SINS.qnb[2] = temp_V4[2]*NAV_Data_Full_p->SINS.qnb[0] - temp_V4[3]*NAV_Data_Full_p->SINS.qnb[1] + temp_V4[0]*NAV_Data_Full_p->SINS.qnb[2] + temp_V4[1]*NAV_Data_Full_p->SINS.qnb[3];
					NAV_Data_Full_p->SINS.qnb[3] = temp_V4[3]*NAV_Data_Full_p->SINS.qnb[0] + temp_V4[2]*NAV_Data_Full_p->SINS.qnb[1] - temp_V4[1]*NAV_Data_Full_p->SINS.qnb[2] + temp_V4[0]*NAV_Data_Full_p->SINS.qnb[3];
			}
			//四元数归一化
			{
					NAV_Data_Full_p->SINS.q_Norm = sqrt(    NAV_Data_Full_p->SINS.qnb[0]*NAV_Data_Full_p->SINS.qnb[0]
													+ NAV_Data_Full_p->SINS.qnb[1]*NAV_Data_Full_p->SINS.qnb[1]
													+ NAV_Data_Full_p->SINS.qnb[2]*NAV_Data_Full_p->SINS.qnb[2]
													+ NAV_Data_Full_p->SINS.qnb[3]*NAV_Data_Full_p->SINS.qnb[3]
												);

					NAV_Data_Full_p->SINS.q_Norm_f = 1/NAV_Data_Full_p->SINS.q_Norm;
					NAV_Data_Full_p->SINS.qnb[0] = NAV_Data_Full_p->SINS.qnb[0]*NAV_Data_Full_p->SINS.q_Norm_f;
					NAV_Data_Full_p->SINS.qnb[1] = NAV_Data_Full_p->SINS.qnb[1]*NAV_Data_Full_p->SINS.q_Norm_f;
					NAV_Data_Full_p->SINS.qnb[2] = NAV_Data_Full_p->SINS.qnb[2]*NAV_Data_Full_p->SINS.q_Norm_f;
					NAV_Data_Full_p->SINS.qnb[3] = NAV_Data_Full_p->SINS.qnb[3]*NAV_Data_Full_p->SINS.q_Norm_f;
			}
			//不同姿态表示方式转换
			{
					qnb2att(NAV_Data_Full_p->SINS.qnb, NAV_Data_Full_p->SINS.att);
					Qnb2Cnb(NAV_Data_Full_p->SINS.qnb, NAV_Data_Full_p->SINS.Cb2n);
					Mat_Tr(3,NAV_Data_Full_p->SINS.Cb2n,NAV_Data_Full_p->SINS.Cn2b);
			}
	}	
	
	
	//杆臂补偿要用到的值
	NAV_Data_Full_p->SINS.Mpv[1] = 1 / NAV_Data_Full_p->EARTH.clRNh; 
	NAV_Data_Full_p->SINS.Mpv[3] = 1 / NAV_Data_Full_p->EARTH.RMh; 
	NAV_Data_Full_p->SINS.Mpv[8] = 1.0;	
	
	
    //数据存buffer
	{
		//qnb
		for(i = 0;i<4;i++)
		{
			NAV_Data_Full_p->SINS_buffer[NAV_Data_Full_p->Head].qnb[i] = NAV_Data_Full_p->SINS.qnb[i];
		}
		//att、vn、pos
		for(i = 0;i<3;i++)
		{
			NAV_Data_Full_p->SINS_buffer[NAV_Data_Full_p->Head].att[i] = NAV_Data_Full_p->SINS.att[i];
			NAV_Data_Full_p->SINS_buffer[NAV_Data_Full_p->Head].vn[i] = NAV_Data_Full_p->SINS.vn[i];
			NAV_Data_Full_p->SINS_buffer[NAV_Data_Full_p->Head].pos[i] = NAV_Data_Full_p->SINS.pos[i];			
		}
		//wnb
		for(i = 0;i<3;i++)
		{
			NAV_Data_Full_p->SINS_buffer[NAV_Data_Full_p->Head].wnb[i] = NAV_Data_Full_p->SINS.wnb[i];			
		}        		
        //Mpv
		for(i = 0;i<9;i++)
		{
			NAV_Data_Full_p->SINS_buffer[NAV_Data_Full_p->Head].Mpv[i] = NAV_Data_Full_p->SINS.Mpv[i];
		}
		//an
		for(i = 0;i<3;i++)
		{
			NAV_Data_Full_p->SINS_buffer[NAV_Data_Full_p->Head].an[i] = NAV_Data_Full_p->SINS.an[i];			
		}
		//平均轮速
		NAV_Data_Full_p->SINS_buffer[NAV_Data_Full_p->Head].WheelSpeed_ave = NAV_Data_Full_p->ODS.WheelSpeed_ave;
		if(++NAV_Data_Full_p->Head>=SINS_BUFFER_SIZE)
		{
			NAV_Data_Full_p->Head = 0;
		}
	}
}

void SINS_UP_DR(_NAV_Data_Full_t* NAV_Data_Full_p)
{
	int i;
	double nts = NAV_Data_Full_p->SINS.nts; //0.005
	double nts_2 = nts*nts;
	double gyro[3], acc[3];
	double wie_b[3],win_b[3];
	//double Cnb[3 * 3];
	//double wb_set[3] = {0*DEG2RAD,0*DEG2RAD,10.23/3600.0*DEG2RAD};
	//double wb_set[3] = {0*DEG2RAD,0*DEG2RAD,8.15/3600.0*DEG2RAD};
	double wb_set[3] = {0*DEG2RAD,0*DEG2RAD,0*DEG2RAD};
    double db_set[3] = {0,0,0};

	//姿态更新中间变量
    double theta_2 = 0;
	double temp = 0;	
	double temp_V4[4] = {0,0,0,0};

	
	//IMU处理	
#if 0	
	gyro[0] = NAV_Data_Full_p->IMU.gyro_use[0] + wb_set[0];
	gyro[1] = NAV_Data_Full_p->IMU.gyro_use[1] + wb_set[1];
	gyro[2] = NAV_Data_Full_p->IMU.gyro_use[2] + wb_set[2];
#endif
	
#ifdef WIN32
	gyro[0] = NAV_Data_Full_p->IMU.gyro_use[0] + NAV_Data_Full_p->Param.wb_set[0]/3600.0*DEG2RAD;
	gyro[1] = NAV_Data_Full_p->IMU.gyro_use[1] + NAV_Data_Full_p->Param.wb_set[1]/3600.0*DEG2RAD;
	gyro[2] = NAV_Data_Full_p->IMU.gyro_use[2] + NAV_Data_Full_p->Param.wb_set[2]/3600.0*DEG2RAD;
#endif
	
	acc[0] = NAV_Data_Full_p->IMU.acc_use[0] + db_set[0];
	acc[1] = NAV_Data_Full_p->IMU.acc_use[1] + db_set[1];
	acc[2] = NAV_Data_Full_p->IMU.acc_use[2] + db_set[2];			

    //扣除估计的零偏 
	for (i = 0; i < 3; i++) 
	{
		gyro[i] = gyro[i] - NAV_Data_Full_p->SINS.eb[i];   //rad/s
		acc[i] = acc[i] - NAV_Data_Full_p->SINS.db[i];  //m/s^2	
	}
//------------------------------------------------------------------------------------
	for (i = 0; i < 3; i++) 
	{
		NAV_Data_Full_p->SINS.wb_ib[i] = gyro[i];
		NAV_Data_Full_p->SINS.fb_ib[i] = acc[i];
	}
	
	
	Earth_UP(NAV_Data_Full_p,NAV_Data_Full_p->SINS.pos, NAV_Data_Full_p->SINS.vn);//更新大地数据

//	Qnb2Cnb(NAV_Data_Full_p->SINS.qnb, Cnb);
	//计算载体坐标系相对大地坐标的角速度补偿
	matmul("TN", 3, 1, 3, 1, NAV_Data_Full_p->SINS.Cb2n, NAV_Data_Full_p->EARTH.wnie,0.0,wie_b);
   //计算载体坐标系相对导航坐标系的角速度补偿
	matmul("TN", 3, 1, 3, 1, NAV_Data_Full_p->SINS.Cb2n, NAV_Data_Full_p->EARTH.wnin, 0.0, win_b);
   //计算导航坐标系下的加速度，尚未补偿重力加速度
	matmul("NN", 3, 1, 3, 1, NAV_Data_Full_p->SINS.Cb2n, NAV_Data_Full_p->SINS.fb_ib,0.0,NAV_Data_Full_p->SINS.fn);
	
	//载体相对导航系的加速度计算
    for(i = 0;i<3;i++)
	{   
	    //计算大地坐标系下的角速度
		NAV_Data_Full_p->SINS.web[i] = NAV_Data_Full_p->SINS.wb_ib[i]- wie_b[i];
		//计算导航系下的角速度
		NAV_Data_Full_p->SINS.wnb[i] = NAV_Data_Full_p->SINS.wb_ib[i]- win_b[i];
		//计算导航系下的加速度，补偿上重力加速度m/s2,举例{-4.0409829786878522e-07, 2.9398186206018674e-07, -9.7879837185144272}
		//参考《捷联惯导算法与组合导航原理》公式4.1.20
		NAV_Data_Full_p->SINS.an[i] = NAV_Data_Full_p->SINS.fn[i] + NAV_Data_Full_p->EARTH.gcc[i];
	}

/////////////////////////////////////////////////////////////////////////////////	
    //位置更新  p1 = p0+(v0+0.5*a*dt)*dt
	NAV_Data_Full_p->SINS.pos[0] = NAV_Data_Full_p->SINS.pos[0] + (NAV_Data_Full_p->SINS.vn[1]+0.5*NAV_Data_Full_p->SINS.an[1]*nts)/NAV_Data_Full_p->EARTH.RMh * nts;
	NAV_Data_Full_p->SINS.pos[1] = NAV_Data_Full_p->SINS.pos[1] + (NAV_Data_Full_p->SINS.vn[0]+0.5*NAV_Data_Full_p->SINS.an[0]*nts)/NAV_Data_Full_p->EARTH.clRNh * nts;
	NAV_Data_Full_p->SINS.pos[2] = NAV_Data_Full_p->SINS.pos[2] + (NAV_Data_Full_p->SINS.vn[2]+0.5*NAV_Data_Full_p->SINS.an[0]*nts) * nts;
	
	
	
////////////////////////////////////////////////////////////////////////////////////	
	//速度更新	v1 = v0+a*dt	
	for(i = 0;i<3;i++)
	{
		NAV_Data_Full_p->SINS.vn[i] = NAV_Data_Full_p->SINS.vn[i] + NAV_Data_Full_p->SINS.an[i]*nts;
	}
///////////////////////////////////////////////////////////////////////////////////
	//姿态更新四阶近似的毕卡迭代法
	{
			//等效旋转矢量计算
			{
					for(i = 0;i<3;i++)
					{
							NAV_Data_Full_p->SINS.dtheta[i] = NAV_Data_Full_p->SINS.wnb[i]*nts;
					}
					//
					{
							temp = 0;
							for(i = 0;i<3;i++)
							{
									temp += NAV_Data_Full_p->SINS.dtheta[i]*NAV_Data_Full_p->SINS.dtheta[i];
							}
					}
					theta_2 = temp;
					
					temp = 0.5-0.02083*theta_2;  
					for(i = 0;i<4;i++)
					{
							if(i == 0)
							{
									temp_V4[0] = 1-0.125*theta_2+0.002604*theta_2*theta_2;
							}
							else
							{
									temp_V4[i] = NAV_Data_Full_p->SINS.dtheta[i-1]*temp;
							}
					}
			}
			//Q_up
			{
					//q_k1 = M'*q_k
					NAV_Data_Full_p->SINS.qnb[0] = temp_V4[0]*NAV_Data_Full_p->SINS.qnb[0] - temp_V4[1]*NAV_Data_Full_p->SINS.qnb[1] - temp_V4[2]*NAV_Data_Full_p->SINS.qnb[2] - temp_V4[3]*NAV_Data_Full_p->SINS.qnb[3];
					NAV_Data_Full_p->SINS.qnb[1] = temp_V4[1]*NAV_Data_Full_p->SINS.qnb[0] + temp_V4[0]*NAV_Data_Full_p->SINS.qnb[1] + temp_V4[3]*NAV_Data_Full_p->SINS.qnb[2] - temp_V4[2]*NAV_Data_Full_p->SINS.qnb[3];
					NAV_Data_Full_p->SINS.qnb[2] = temp_V4[2]*NAV_Data_Full_p->SINS.qnb[0] - temp_V4[3]*NAV_Data_Full_p->SINS.qnb[1] + temp_V4[0]*NAV_Data_Full_p->SINS.qnb[2] + temp_V4[1]*NAV_Data_Full_p->SINS.qnb[3];
					NAV_Data_Full_p->SINS.qnb[3] = temp_V4[3]*NAV_Data_Full_p->SINS.qnb[0] + temp_V4[2]*NAV_Data_Full_p->SINS.qnb[1] - temp_V4[1]*NAV_Data_Full_p->SINS.qnb[2] + temp_V4[0]*NAV_Data_Full_p->SINS.qnb[3];
			}
			//四元数归一化
			{
					NAV_Data_Full_p->SINS.q_Norm = sqrt(    NAV_Data_Full_p->SINS.qnb[0]*NAV_Data_Full_p->SINS.qnb[0]
													+ NAV_Data_Full_p->SINS.qnb[1]*NAV_Data_Full_p->SINS.qnb[1]
													+ NAV_Data_Full_p->SINS.qnb[2]*NAV_Data_Full_p->SINS.qnb[2]
													+ NAV_Data_Full_p->SINS.qnb[3]*NAV_Data_Full_p->SINS.qnb[3]
												);
					NAV_Data_Full_p->SINS.q_Norm_f = 1/NAV_Data_Full_p->SINS.q_Norm;
					NAV_Data_Full_p->SINS.qnb[0] = NAV_Data_Full_p->SINS.qnb[0]*NAV_Data_Full_p->SINS.q_Norm_f;
					NAV_Data_Full_p->SINS.qnb[1] = NAV_Data_Full_p->SINS.qnb[1]*NAV_Data_Full_p->SINS.q_Norm_f;
					NAV_Data_Full_p->SINS.qnb[2] = NAV_Data_Full_p->SINS.qnb[2]*NAV_Data_Full_p->SINS.q_Norm_f;
					NAV_Data_Full_p->SINS.qnb[3] = NAV_Data_Full_p->SINS.qnb[3]*NAV_Data_Full_p->SINS.q_Norm_f;
			}
			//不同姿态表示方式转换
			{
					qnb2att(NAV_Data_Full_p->SINS.qnb, NAV_Data_Full_p->SINS.att);
					Qnb2Cnb(NAV_Data_Full_p->SINS.qnb, NAV_Data_Full_p->SINS.Cb2n);
					Mat_Tr(3,NAV_Data_Full_p->SINS.Cb2n,NAV_Data_Full_p->SINS.Cn2b);
			}
	}	
	
	
	//杆臂补偿要用到的值
	NAV_Data_Full_p->SINS.Mpv[1] = 1 / NAV_Data_Full_p->EARTH.clRNh; 
	NAV_Data_Full_p->SINS.Mpv[3] = 1 / NAV_Data_Full_p->EARTH.RMh; 
	NAV_Data_Full_p->SINS.Mpv[8] = 1.0;	
	
	
    //数据存buffer
	{
		//qnb
		for(i = 0;i<4;i++)
		{
			NAV_Data_Full_p->SINS_buffer[NAV_Data_Full_p->Head].qnb[i] = NAV_Data_Full_p->SINS.qnb[i];
		}
		//att、vn、pos
		for(i = 0;i<3;i++)
		{
			NAV_Data_Full_p->SINS_buffer[NAV_Data_Full_p->Head].att[i] = NAV_Data_Full_p->SINS.att[i];
			NAV_Data_Full_p->SINS_buffer[NAV_Data_Full_p->Head].vn[i] = NAV_Data_Full_p->SINS.vn[i];
			NAV_Data_Full_p->SINS_buffer[NAV_Data_Full_p->Head].pos[i] = NAV_Data_Full_p->SINS.pos[i];			
		}
		//wnb
		for(i = 0;i<3;i++)
		{
			NAV_Data_Full_p->SINS_buffer[NAV_Data_Full_p->Head].wnb[i] = NAV_Data_Full_p->SINS.wnb[i];			
		}        		
        //Mpv
		for(i = 0;i<9;i++)
		{
			NAV_Data_Full_p->SINS_buffer[NAV_Data_Full_p->Head].Mpv[i] = NAV_Data_Full_p->SINS.Mpv[i];
		}
		//an
		for(i = 0;i<3;i++)
		{
			NAV_Data_Full_p->SINS_buffer[NAV_Data_Full_p->Head].an[i] = NAV_Data_Full_p->SINS.an[i];			
		}
		if(++NAV_Data_Full_p->Head>=SINS_BUFFER_SIZE)
		{
			NAV_Data_Full_p->Head = 0;
		}
	}
}
void vector_unitization(double* vec)
{
	double vec_norm;
	vec_norm = sqrt(vec[0] * vec[0]+ vec[1] * vec[1]+ vec[2] * vec[2]);
	if (vec_norm > 0) 
	{
		vec[0] = vec[0] / vec_norm;
		vec[1] = vec[1] / vec_norm;
		vec[2] = vec[2] / vec_norm;
	}
	
}
void MahonyUpdate_NoMAG_fuse(_NAV_Data_Full_t* NAV_Data_Full_p)
{
	int i=0;
	double tmp_acc[3]={0};
	double tmp_acc_use[3] = {0};
	double err_acc[3]={0};
	double motion_factor = 0.0;
	double acc_magnitude = 0.0;
	double MAX_INTEGRAL = 0.01;	
	Mat_Tr(3, NAV_Data_Full_p->SINS.Cb2n, NAV_Data_Full_p->SINS.Cn2b);//n系转b系方向余弦阵
	matmul("NN", 3, 1, 3, 1.0, NAV_Data_Full_p->SINS.Cn2b, NAV_Data_Full_p->EARTH.gn, 0.0, tmp_acc);//重力矢量转换
	vector_unitization(tmp_acc);//单位化
	for (int i = 0; i < 3; i++) 
	{
		tmp_acc_use[i] = NAV_Data_Full_p->IMU.acc_use[i];
	}
	vector_unitization(tmp_acc_use);
	cross3(tmp_acc, tmp_acc_use,err_acc);//向量叉积得到误差
	///**************************Kp KI参数设置************************/
	double tau = 5; //不加 加速度限制条件，5.2是合适的。
	double Kp = 2 * (2.146 / tau); //0.8
	double Ki = (2.146 / tau) * (2.146 / tau);
	double  acc_norm = sqrt(NAV_Data_Full_p->IMU.acc_use[0] * NAV_Data_Full_p->IMU.acc_use[0] +
		NAV_Data_Full_p->IMU.acc_use[1] * NAV_Data_Full_p->IMU.acc_use[1] +
		NAV_Data_Full_p->IMU.acc_use[2] * NAV_Data_Full_p->IMU.acc_use[2]);
	 acc_magnitude = fabs(acc_norm - G0);
	//acc_magnitude = sqrt(NAV_Data_Full_p->IMU.acc_use[0] * NAV_Data_Full_p->IMU.acc_use[0] +
		//NAV_Data_Full_p->IMU.acc_use[1] * NAV_Data_Full_p->IMU.acc_use[1] +
		//(NAV_Data_Full_p->IMU.acc_use[2] - G0) * (NAV_Data_Full_p->IMU.acc_use[2] - G0));
	//acc_magnitude = fabs(acc_magnitude);//与静止时的重力矢量比较选择不同Kp系数
	if (acc_magnitude > 4)
	{
		Kp = 0;
	}
	else if (acc_magnitude > 3)
	{
		Kp = Kp * 0.5;
	}
	else if (acc_magnitude > 2)
	{
		Kp = Kp * 0.8;
	}
	else if (acc_magnitude  < 1)
	{
		Kp = Kp * 3;
	}

	/*************************修正陀螺仪输出值*****************************/
	double err_both[3] = { 0.0 };
	double deltap[3] = { 0.0 };
	double deltai[3] = { 0.0 };
	//matrixSum(err_acc, err_mag, 3, 1, 1.0, err_both);
	err_both[0]=err_acc[0];
	err_both[1]=err_acc[1];//三个误差值
	//err_both[2]=err_acc[2];
	err_both[2] = 0;
	//只在加速度模值较小时对航向进行修正，防止错误修正航向
	//if (acc_magnitude_filtered < 0.5)
	//{
	//	err_both[2] = err_acc[2];
	//}
	//根据pi调节，设置对陀螺测量值的修正量：
	//根据当前运动情况,设置kp,ki
	//SetPidParmByMotion(NAV_Data_Full_p,&g_NAV_MAHONY);
	//P调节修正量
	for(i=0;i<3;i++)
	{
		deltap[i] = Kp * err_both[i];
	}                                       
	//i调节修正量
	for(i=0;i<3;i++)
	{
		deltai[i]=Ki*err_both[i]*NAV_Data_Full_p->SINS.ts;//误差乘以。。。
		//对积分修正量进行限值
		if (deltai[i] > MAX_INTEGRAL) deltai[i] = MAX_INTEGRAL;
		if (deltai[i] < -MAX_INTEGRAL) deltai[i] = -MAX_INTEGRAL;
	}
	////对陀螺仪测量值进行修正：
	//double Gyrk[3] = { 0.0,0.0,0.0 };
	//for(i=0;i<3;i++)
	//{
	//	Gyrk[i] = NAV_Data_Full_p->IMU.gyro_use[i];
	//	Gyrk[i] = Gyrk[i] + deltap[i] + deltai[i];
	// 	
	//}
	//double phim[3]={0.0};
	////计算姿态
	//for(i=0;i<3;i++)
	//{
	//	phim[i]= Gyrk[i]*NAV_Data_Full_p->SINS.ts;//rad
	//}
	//
	//SINS_UP_ATT(NAV_Data_Full_p,phim);




	//四元数更新角增量
	double nts = NAV_Data_Full_p->SINS.nts; //0.005
	double nts_2 = nts * nts;
	double gyro[3], acc[3];
	double wib[3], fb[3], wie_b[3], win_b[3];
	double Cnb[3 * 3];
	double wb_set[3] = { 0 * DEG2RAD,0 * DEG2RAD,0 * DEG2RAD };
	double db_set[3] = { 0,0,0 };

	//NAV_Data_Full_p->IMU.gyro_use[2] = 0;
	//deltap[2] = 0;

	double calib[3] = { 0 };
	gyro[0] = NAV_Data_Full_p->IMU.gyro_use[0] + wb_set[0];
	gyro[1] = NAV_Data_Full_p->IMU.gyro_use[1] + wb_set[1];
	gyro[2] = NAV_Data_Full_p->IMU.gyro_use[2] + wb_set[2];
	calib[0] = deltap[0] + deltai[0] ;
	calib[1] = deltap[1] + deltai[1] ;
	calib[2] = deltap[2] + deltai[2] ;
	calib[2] = 0;
#if 0
	if (fabs(NAV_Data_Full_p->IMU.gyro_use[0]) < 0.05 * DEG2RAD)
	{
		gyro[0] = gyro[0] - NAV_Data_Full_p->IMU.gyro_use[0];
	}
	if (fabs(NAV_Data_Full_p->IMU.gyro_use[1]) < 0.05 * DEG2RAD)
	{
		gyro[1] = gyro[1] - NAV_Data_Full_p->IMU.gyro_use[1];
	}
	if (fabs(NAV_Data_Full_p->IMU.gyro_use[2]) < 0.05 * DEG2RAD)
	{
		gyro[2] = gyro[2] - NAV_Data_Full_p->IMU.gyro_use[2];
	}
#endif
	acc[0] = NAV_Data_Full_p->IMU.acc_use[0] + db_set[0];
	acc[1] = NAV_Data_Full_p->IMU.acc_use[1] + db_set[1];
	acc[2] = NAV_Data_Full_p->IMU.acc_use[2] + db_set[2];
	for (i = 0; i < 3; i++)
	{
		gyro[i] = gyro[i] - NAV_Data_Full_p->SINS.eb[i];   //rad/s
		acc[i] = acc[i] - NAV_Data_Full_p->SINS.db[i];  //m/s^2	
	}
	//------------------------------------------------------------------------------------
	for (i = 0; i < 3; i++)
	{
		//NAV_Data_Full_p->SINS.wb_ib[i] = gyro[i];
		//NAV_Data_Full_p->SINS.fb_ib[i] = acc[i];
		NAV_Data_Full_p->SINS.wnb_pre[i] = NAV_Data_Full_p->SINS.wnb[i];
		NAV_Data_Full_p->SINS.wnb[i] = gyro[i];

	}

	if (0)
	{
		//Earth_UP(NAV_Data_Full_p,NAV_Data_Full_p->SINS.pos, NAV_Data_Full_p->SINS.vn);//更新大地数据

		//Qnb2Cnb(NAV_Data_Full_p->SINS.qnb, Cnb);
		//计算载体坐标系相对大地坐标的角速度补偿
		//matmul("TN", 3, 1, 3, 1, NAV_Data_Full_p->SINS.Cb2n, NAV_Data_Full_p->EARTH.wnie,0.0,wie_b);
	   //计算载体坐标系相对导航坐标系的角速度补偿
		matmul("TN", 3, 1, 3, 1, NAV_Data_Full_p->SINS.Cb2n, NAV_Data_Full_p->EARTH.wnin, 0.0, win_b);
		//计算导航坐标系下的加速度，尚未补偿重力加速度
		matmul("NN", 3, 1, 3, 1, NAV_Data_Full_p->SINS.Cb2n, NAV_Data_Full_p->SINS.fb_ib, 0.0, NAV_Data_Full_p->SINS.fn);

		//载体相对导航系的加速度计算
		for (i = 0; i < 3; i++)
		{
			wie_b[i] = 0;
			win_b[i] = 0;
			//计算大地坐标系下的角速度
			NAV_Data_Full_p->SINS.web[i] = NAV_Data_Full_p->SINS.wb_ib[i] - wie_b[i];
			//计算导航系下的角速度
			NAV_Data_Full_p->SINS.wnb[i] = NAV_Data_Full_p->SINS.wb_ib[i] - win_b[i];
			//计算导航系下的加速度，补偿上重力加速度m/s2,举例{-4.0409829786878522e-07, 2.9398186206018674e-07, -9.7879837185144272}
			//
			//NAV_Data_Full_p->SINS.an[i] = NAV_Data_Full_p->SINS.fn[i] + NAV_Data_Full_p->EARTH.gcc[i];
		}

	}
	if (fabs(NAV_Data_Full_p->IMU.gyro_use[0]) < 0.15 * DEG2RAD
		&& fabs(NAV_Data_Full_p->IMU.gyro_use[1]) < 0.15 * DEG2RAD
		&& fabs(NAV_Data_Full_p->IMU.gyro_use[2]) < 0.15 * DEG2RAD
		)
	{
		NAV_Data_Full_p->SINS.wnb[0] = 0;
		NAV_Data_Full_p->SINS.wnb[1] = 0;
		NAV_Data_Full_p->SINS.wnb[2] = 0;
		calib[0] = 0;
		calib[1] = 0;
		calib[2] = 0;
	}
	//单子样加前一周期求等效旋转矢量
	double phi[3] = { 0 };
	for (i = 0; i < 3; i++)
	{
		phi[i] = NAV_Data_Full_p->SINS.wnb[i];
	}
	if (1)
	{
		cross3(NAV_Data_Full_p->SINS.wnb_pre, NAV_Data_Full_p->SINS.wnb, phi);
		for (i = 0; i < 3; i++)
		{
			phi[i] = NAV_Data_Full_p->SINS.wnb[i] * nts + phi[i] / 12 * nts * nts;
		}
	}


	//姿态更新中间变量
	double theta_2 = 0;
	double temp = 0;
	double temp_V4[4] = { 0,0,0,0 };
	//四阶毕卡姿态更新		，陀螺更新
	{
		for (i = 0; i < 3; i++)
		{
			if (1)
				NAV_Data_Full_p->SINS.dtheta[i] = phi[i];
			else
				NAV_Data_Full_p->SINS.dtheta[i] = NAV_Data_Full_p->SINS.wnb[i] * nts;
		}
		//
		{
			temp = 0;
			for (i = 0; i < 3; i++)
			{
				temp += NAV_Data_Full_p->SINS.dtheta[i] * NAV_Data_Full_p->SINS.dtheta[i];
			}
		}
		theta_2 = temp;

		temp = 0.5 - 0.02083 * theta_2;
		for (i = 0; i < 4; i++)
		{
			if (i == 0)
			{
				temp_V4[0] = 1 - 0.125 * theta_2 + 0.002604 * theta_2 * theta_2;
			}
			else
			{
				temp_V4[i] = NAV_Data_Full_p->SINS.dtheta[i - 1] * temp;
			}
		}
	}
	//Q_up
	{
		//q_k1 = M'*q_k
		NAV_Data_Full_p->SINS.qnb[0] = temp_V4[0] * NAV_Data_Full_p->SINS.qnb[0] - temp_V4[1] * NAV_Data_Full_p->SINS.qnb[1] - temp_V4[2] * NAV_Data_Full_p->SINS.qnb[2] - temp_V4[3] * NAV_Data_Full_p->SINS.qnb[3];
		NAV_Data_Full_p->SINS.qnb[1] = temp_V4[1] * NAV_Data_Full_p->SINS.qnb[0] + temp_V4[0] * NAV_Data_Full_p->SINS.qnb[1] + temp_V4[3] * NAV_Data_Full_p->SINS.qnb[2] - temp_V4[2] * NAV_Data_Full_p->SINS.qnb[3];
		NAV_Data_Full_p->SINS.qnb[2] = temp_V4[2] * NAV_Data_Full_p->SINS.qnb[0] - temp_V4[3] * NAV_Data_Full_p->SINS.qnb[1] + temp_V4[0] * NAV_Data_Full_p->SINS.qnb[2] + temp_V4[1] * NAV_Data_Full_p->SINS.qnb[3];
		NAV_Data_Full_p->SINS.qnb[3] = temp_V4[3] * NAV_Data_Full_p->SINS.qnb[0] + temp_V4[2] * NAV_Data_Full_p->SINS.qnb[1] - temp_V4[1] * NAV_Data_Full_p->SINS.qnb[2] + temp_V4[0] * NAV_Data_Full_p->SINS.qnb[3];
	}
	//四元数归一化
	{
		NAV_Data_Full_p->SINS.q_Norm = sqrt(NAV_Data_Full_p->SINS.qnb[0] * NAV_Data_Full_p->SINS.qnb[0]
			+ NAV_Data_Full_p->SINS.qnb[1] * NAV_Data_Full_p->SINS.qnb[1]
			+ NAV_Data_Full_p->SINS.qnb[2] * NAV_Data_Full_p->SINS.qnb[2]
			+ NAV_Data_Full_p->SINS.qnb[3] * NAV_Data_Full_p->SINS.qnb[3]
		);
		NAV_Data_Full_p->SINS.q_Norm_f = 1 / NAV_Data_Full_p->SINS.q_Norm;
		NAV_Data_Full_p->SINS.qnb[0] = NAV_Data_Full_p->SINS.qnb[0] * NAV_Data_Full_p->SINS.q_Norm_f;
		NAV_Data_Full_p->SINS.qnb[1] = NAV_Data_Full_p->SINS.qnb[1] * NAV_Data_Full_p->SINS.q_Norm_f;
		NAV_Data_Full_p->SINS.qnb[2] = NAV_Data_Full_p->SINS.qnb[2] * NAV_Data_Full_p->SINS.q_Norm_f;
		NAV_Data_Full_p->SINS.qnb[3] = NAV_Data_Full_p->SINS.qnb[3] * NAV_Data_Full_p->SINS.q_Norm_f;
	}
	qnb2att(NAV_Data_Full_p->SINS.qnb, NAV_Data_Full_p->SINS.att);

	//四阶毕卡姿态更新		 加速度计观测误差
	if (fabs(NAV_Data_Full_p->IMU.gyro_use[2]) < 30 * DEG2RAD)

	{
		for (i = 0; i < 3; i++)
		{
			NAV_Data_Full_p->SINS.dtheta[i] = calib[i] * nts;
		}
		//
		{
			temp = 0;
			for (i = 0; i < 3; i++)
			{
				temp += NAV_Data_Full_p->SINS.dtheta[i] * NAV_Data_Full_p->SINS.dtheta[i];
			}
		}
		theta_2 = temp;

		temp = 0.5 - 0.02083 * theta_2;
		for (i = 0; i < 4; i++)
		{
			if (i == 0)
			{
				temp_V4[0] = 1 - 0.125 * theta_2 + 0.002604 * theta_2 * theta_2;
			}
			else
			{
				temp_V4[i] = NAV_Data_Full_p->SINS.dtheta[i - 1] * temp;
			}
		}

		//Q_up
		{
			//q_k1 = M'*q_k
			NAV_Data_Full_p->SINS.qnb[0] = temp_V4[0] * NAV_Data_Full_p->SINS.qnb[0] - temp_V4[1] * NAV_Data_Full_p->SINS.qnb[1] - temp_V4[2] * NAV_Data_Full_p->SINS.qnb[2] - temp_V4[3] * NAV_Data_Full_p->SINS.qnb[3];
			NAV_Data_Full_p->SINS.qnb[1] = temp_V4[1] * NAV_Data_Full_p->SINS.qnb[0] + temp_V4[0] * NAV_Data_Full_p->SINS.qnb[1] + temp_V4[3] * NAV_Data_Full_p->SINS.qnb[2] - temp_V4[2] * NAV_Data_Full_p->SINS.qnb[3];
			NAV_Data_Full_p->SINS.qnb[2] = temp_V4[2] * NAV_Data_Full_p->SINS.qnb[0] - temp_V4[3] * NAV_Data_Full_p->SINS.qnb[1] + temp_V4[0] * NAV_Data_Full_p->SINS.qnb[2] + temp_V4[1] * NAV_Data_Full_p->SINS.qnb[3];
			NAV_Data_Full_p->SINS.qnb[3] = temp_V4[3] * NAV_Data_Full_p->SINS.qnb[0] + temp_V4[2] * NAV_Data_Full_p->SINS.qnb[1] - temp_V4[1] * NAV_Data_Full_p->SINS.qnb[2] + temp_V4[0] * NAV_Data_Full_p->SINS.qnb[3];
		}
		//四元数归一化
		{
			NAV_Data_Full_p->SINS.q_Norm = sqrt(NAV_Data_Full_p->SINS.qnb[0] * NAV_Data_Full_p->SINS.qnb[0]
				+ NAV_Data_Full_p->SINS.qnb[1] * NAV_Data_Full_p->SINS.qnb[1]
				+ NAV_Data_Full_p->SINS.qnb[2] * NAV_Data_Full_p->SINS.qnb[2]
				+ NAV_Data_Full_p->SINS.qnb[3] * NAV_Data_Full_p->SINS.qnb[3]
			);
			NAV_Data_Full_p->SINS.q_Norm_f = 1 / NAV_Data_Full_p->SINS.q_Norm;
			NAV_Data_Full_p->SINS.qnb[0] = NAV_Data_Full_p->SINS.qnb[0] * NAV_Data_Full_p->SINS.q_Norm_f;
			NAV_Data_Full_p->SINS.qnb[1] = NAV_Data_Full_p->SINS.qnb[1] * NAV_Data_Full_p->SINS.q_Norm_f;
			NAV_Data_Full_p->SINS.qnb[2] = NAV_Data_Full_p->SINS.qnb[2] * NAV_Data_Full_p->SINS.q_Norm_f;
			NAV_Data_Full_p->SINS.qnb[3] = NAV_Data_Full_p->SINS.qnb[3] * NAV_Data_Full_p->SINS.q_Norm_f;
		}
		//	double temp_ATT[3] = {0};
		//	qnb2att(NAV_Data_Full_p->SINS.qnb, temp_ATT);
		//	temp_ATT[2] = NAV_Data_Full_p->SINS.att[2];
		//	att2qnb2(temp_ATT,NAV_Data_Full_p->SINS.qnb);
	}
	//不同姿态表示方式转换
	{
		qnb2att(NAV_Data_Full_p->SINS.qnb, NAV_Data_Full_p->SINS.att);
		Qnb2Cnb(NAV_Data_Full_p->SINS.qnb, NAV_Data_Full_p->SINS.Cb2n);
		Mat_Tr(3, NAV_Data_Full_p->SINS.Cb2n, NAV_Data_Full_p->SINS.Cn2b);
		//NAV_Data_Full_p->SINS.att_deg[0] = NAV_Data_Full_p->SINS.att[0] * RAD2DEG;
		//NAV_Data_Full_p->SINS.att_deg[1] = NAV_Data_Full_p->SINS.att[1] * RAD2DEG;
		//NAV_Data_Full_p->SINS.att_deg[2] = NAV_Data_Full_p->SINS.att[2] * RAD2DEG;
	}

	
}
////////////////////////////////end/////////////////////////////////////////

//%**********20240701*****design by GuoLong Zhang*******双子样更新*********************->***********
void SINS_Update(_NAV_Data_Full_t* NAV_Data_Full_p)
{
	NAV_Data_Full_p->Pre_att_flag = RETURN_SUCESS;//已经进入KF组合状态，置1
	int i,j;
	double nts = NAV_Data_Full_p->SINS.nts; //0.005
	double gyro[3]={0}, acc[3]={0};
	double gyro_pre[3]={0}, acc_pre[3]={0};
 double rotin[3]={0},rotib[3]={0};  
	double dst1[3]={0},dst2[3]={0},dst[3]={0},atmp1[3]={0};
	double DVgcorm[3]={0};
	double DV1[3]={0},DV2[3]={0},DVm[3]={0},DVrot[3]={0},DVscul[3]={0},DVsfb[3]={0},DVsfm[3]={0};
	double vtmp1[3]={0},vtmp2[3]={0},vn_pre[3]={0};
	double DltR[3]={0};
		//IMU处理
		gyro[0]=NAV_Data_Full_p->IMU.gyro_use[0];
		gyro[1]=NAV_Data_Full_p->IMU.gyro_use[1];
		gyro[2]=NAV_Data_Full_p->IMU.gyro_use[2];
		acc[0]=NAV_Data_Full_p->IMU.acc_use[0]/G0*fabs(NAV_Data_Full_p->EARTH.gn[2]);
		acc[1]=NAV_Data_Full_p->IMU.acc_use[1]/G0*fabs(NAV_Data_Full_p->EARTH.gn[2]);
		acc[2]=NAV_Data_Full_p->IMU.acc_use[2]/G0*fabs(NAV_Data_Full_p->EARTH.gn[2]);
		
		gyro_pre[0]=NAV_Data_Full_p->IMU.gyro_use_pre[0];
		gyro_pre[1]=NAV_Data_Full_p->IMU.gyro_use_pre[1];
		gyro_pre[2]=NAV_Data_Full_p->IMU.gyro_use_pre[2];
		acc_pre[0]=NAV_Data_Full_p->IMU.acc_use_pre[0]/G0*fabs(NAV_Data_Full_p->EARTH.gn[2]);
		acc_pre[1]=NAV_Data_Full_p->IMU.acc_use_pre[1]/G0*fabs(NAV_Data_Full_p->EARTH.gn[2]);
		acc_pre[2]=NAV_Data_Full_p->IMU.acc_use_pre[2]/G0*fabs(NAV_Data_Full_p->EARTH.gn[2]);
 //*****
	for (i = 0; i < 3; i++) 
	{
		//NAV_Data_Full_p->SINS.eb[i] = 0.99999861 * NAV_Data_Full_p->SINS.eb[i];
		//NAV_Data_Full_p->SINS.db[i] = 0.99999861 * NAV_Data_Full_p->SINS.db[i];
		gyro[i] = gyro[i] - NAV_Data_Full_p->SINS.eb[i];   //rad/s
		acc[i] = acc[i] - NAV_Data_Full_p->SINS.db[i];  //m/s^2	
		
		gyro_pre[i] = gyro_pre[i] -NAV_Data_Full_p->SINS.eb[i];  
		acc_pre[i] = acc_pre[i] - NAV_Data_Full_p->SINS.db[i];  
	}
	//************************att update******->******
	for (i = 0; i < 3; i++)
	{
		rotin[i]=NAV_Data_Full_p->EARTH.wnin[i]*nts;
	}
	//
	for (i = 0; i < 3; i++)
	{
		dst1[i]=(3.0/8*gyro_pre[i]+1.0/8*gyro[i])*nts;
		dst2[i]=(1.0/8*gyro_pre[i]+3.0/8*gyro[i])*nts;
		dst[i]=dst1[i]+dst2[i];
	}
	cross3(dst1,dst2,atmp1);
	rotib[0]=dst[0]+2.0/3*atmp1[0];
	rotib[1]=dst[1]+2.0/3*atmp1[1];//***包含圆锥误差***
	rotib[2]=dst[2]+2.0/3*atmp1[2];
	//matrixSum(dst, atmp1, 3, 1, 1.0, rotib) ; //矩阵求和*****减少函数调用*****
	UpdateQnb(NAV_Data_Full_p->SINS.qnb, rotib, rotin);
	//************************att update********<-****
	//************************vel update*******->*****	
	for (i = 0; i < 3; i++)
	{
		DVgcorm[i]=NAV_Data_Full_p->EARTH.gcc[i]*nts;
	}
  for (i = 0; i < 3; i++)
	{
		DV1[i]=(3.0/8*acc_pre[i]+1.0/8*acc[i])*nts;
		DV2[i]=(1.0/8*acc_pre[i]+3.0/8*acc[i])*nts;
		DVm[i]=DV1[i]+DV2[i];
	}
	cross3(dst,DVm,DVrot);//**旋转效应**
	cross3(DV1,dst2,vtmp1);
	cross3(DV2,dst1,vtmp2);//*****划桨效应***
	for (i = 0; i < 3; i++)
	{
		DVsfb[i]=DVm[i]+0.5*DVrot[i]+2.0/3*(vtmp1[i]-vtmp2[i]);
	}
	matmul("NN", 3, 1, 3, 1.0, NAV_Data_Full_p->SINS.Cb2n, DVsfb,0.0,DVsfm);
	vn_pre[0]=NAV_Data_Full_p->SINS.vn[0]; 
	vn_pre[1]=NAV_Data_Full_p->SINS.vn[1]; 
	vn_pre[2]=NAV_Data_Full_p->SINS.vn[2];
	for (i = 0; i < 3; i++)
	{
		NAV_Data_Full_p->SINS.vn[i]=vn_pre[i]+DVgcorm[i]+DVsfm[i];
	}
	//************************vel update********<-****
	//************************pos update********->****位置更新中旋转，涡卷较小，无需补偿！****
	 for (i = 0; i < 3; i++)
	{
		DltR[i]=(vn_pre[i]+NAV_Data_Full_p->SINS.vn[i])*nts/2.0;
	}
		NAV_Data_Full_p->SINS.pos_pre[0] = NAV_Data_Full_p->SINS.pos[0];
		NAV_Data_Full_p->SINS.pos_pre[1] = NAV_Data_Full_p->SINS.pos[1];
		NAV_Data_Full_p->SINS.pos_pre[2] = NAV_Data_Full_p->SINS.pos[2];
	 NAV_Data_Full_p->SINS.pos[0] = NAV_Data_Full_p->SINS.pos_pre[0] + DltR[1]/ NAV_Data_Full_p->EARTH.RMh;
	 NAV_Data_Full_p->SINS.pos[1] = NAV_Data_Full_p->SINS.pos_pre[1] + DltR[0]/NAV_Data_Full_p->EARTH.clRNh;
	 NAV_Data_Full_p->SINS.pos[2] = NAV_Data_Full_p->SINS.pos_pre[2] + DltR[2];
	//************************pos update*********<-***
	Earth_UP(NAV_Data_Full_p,NAV_Data_Full_p->SINS.pos, NAV_Data_Full_p->SINS.vn);//更新大地数据***OK!!***
	//杆臂补偿要用到的值
	NAV_Data_Full_p->SINS.Mpv[1] = 1.0 / NAV_Data_Full_p->EARTH.clRNh; 
	NAV_Data_Full_p->SINS.Mpv[3] = 1.0 / NAV_Data_Full_p->EARTH.RMh; 
	NAV_Data_Full_p->SINS.Mpv[8] = 1.0;	
				//四元数归一化
	NAV_Data_Full_p->SINS.q_Norm = sqrt( NAV_Data_Full_p->SINS.qnb[0]*NAV_Data_Full_p->SINS.qnb[0]
									+ NAV_Data_Full_p->SINS.qnb[1]*NAV_Data_Full_p->SINS.qnb[1]
									+ NAV_Data_Full_p->SINS.qnb[2]*NAV_Data_Full_p->SINS.qnb[2]
									+ NAV_Data_Full_p->SINS.qnb[3]*NAV_Data_Full_p->SINS.qnb[3]
								);

	NAV_Data_Full_p->SINS.q_Norm_f = 1.0/NAV_Data_Full_p->SINS.q_Norm;
	NAV_Data_Full_p->SINS.qnb[0] = NAV_Data_Full_p->SINS.qnb[0]*NAV_Data_Full_p->SINS.q_Norm_f;
	NAV_Data_Full_p->SINS.qnb[1] = NAV_Data_Full_p->SINS.qnb[1]*NAV_Data_Full_p->SINS.q_Norm_f;
	NAV_Data_Full_p->SINS.qnb[2] = NAV_Data_Full_p->SINS.qnb[2]*NAV_Data_Full_p->SINS.q_Norm_f;
	NAV_Data_Full_p->SINS.qnb[3] = NAV_Data_Full_p->SINS.qnb[3]*NAV_Data_Full_p->SINS.q_Norm_f;
			//不同姿态表示方式转换
	qnb2att(NAV_Data_Full_p->SINS.qnb, NAV_Data_Full_p->SINS.att);  
	Qnb2Cnb(NAV_Data_Full_p->SINS.qnb, NAV_Data_Full_p->SINS.Cb2n);
	Mat_Tr(3,NAV_Data_Full_p->SINS.Cb2n,NAV_Data_Full_p->SINS.Cn2b);
	//*******载体姿态*********
	qnbmul(NAV_Data_Full_p->SINS.qnb, NAV_Data_Full_p->SubKF.Q_b2m, NAV_Data_Full_p->Q_body); 
	qnb2att(NAV_Data_Full_p->Q_body, NAV_Data_Full_p->att_body); 

	//

	for (i = 0; i < 3; i++) 
	{
		NAV_Data_Full_p->SINS.wb_ib[i] = gyro[i];
		NAV_Data_Full_p->SINS.fb_ib[i] = acc[i];
	}
	   //计算导航坐标系下的加速度，尚未补偿重力加速度
	matmul("NN", 3, 1, 3, 1, NAV_Data_Full_p->SINS.Cb2n, NAV_Data_Full_p->SINS.fb_ib,0.0,NAV_Data_Full_p->SINS.fn);
	matrixSum(NAV_Data_Full_p->SINS.fn, NAV_Data_Full_p->EARTH.gcc, 3, 1, 1.0, NAV_Data_Full_p->SINS.an) ; //矩阵求和
	//数据存buffer******根据实际情况确定该缓存数组是否需要******->*****
	//double sum_an[3] = { 0.0,0.0,0.0 }, sum_vn[3] = { 0.0,0.0,0.0 };
	//an vn
	//for (i = 0; i < 3; i++)
	//{
	//	NAV_Data_Full_p->SINS_buffer[NAV_Data_Full_p->Head].an[i] = NAV_Data_Full_p->SINS.an[i];
	//	NAV_Data_Full_p->SINS_buffer[NAV_Data_Full_p->Head].vn[i] = NAV_Data_Full_p->SINS.vn[i];
	//}
	
	/*
		//qnb
		for(i = 0;i<4;i++)
		{
			NAV_Data_Full_p->SINS_buffer[NAV_Data_Full_p->Head].qnb[i] = NAV_Data_Full_p->SINS.qnb[i];
		}
		//att、vn、pos
		for(i = 0;i<3;i++)
		{
			NAV_Data_Full_p->SINS_buffer[NAV_Data_Full_p->Head].att[i] = NAV_Data_Full_p->SINS.att[i];
			NAV_Data_Full_p->SINS_buffer[NAV_Data_Full_p->Head].vn[i] = NAV_Data_Full_p->SINS.vn[i];
			NAV_Data_Full_p->SINS_buffer[NAV_Data_Full_p->Head].pos[i] = NAV_Data_Full_p->SINS.pos[i];			
		}
		//wnb
		
//		for(i = 0;i<3;i++)
//		{
//			NAV_Data_Full_p->SINS_buffer[NAV_Data_Full_p->Head].wnb[i] = NAV_Data_Full_p->SINS.wnb[i];			
//		}        		
        //Mpv
		for(i = 0;i<9;i++)
		{
			NAV_Data_Full_p->SINS_buffer[NAV_Data_Full_p->Head].Mpv[i] = NAV_Data_Full_p->SINS.Mpv[i];
		}
		//平均轮速
		if(NAV_Data_Full_p->ODS.ods_flag)
		{
					NAV_Data_Full_p->SINS_buffer[NAV_Data_Full_p->Head].WheelSpeed_ave = NAV_Data_Full_p->ODS.WheelSpeed_ave;
		}
  //
		*/
	//if(++NAV_Data_Full_p->Head>=SINS_BUFFER_SIZE)
	//{
	//	NAV_Data_Full_p->Head = 0;
	//}
	 //数据存buffer******根据实际情况确定该缓存数组是否需要******<-*****
	 // MahonyUpdate_NoMAG_fuse(NAV_Data_Full_p); // 注释掉，避免重复姿态更新导致发散
}
//%**********20240701*****design by GuoLong Zhang*******双子样更新**********************<-**********

















