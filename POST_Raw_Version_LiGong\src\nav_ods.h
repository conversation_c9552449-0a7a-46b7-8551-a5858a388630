/***********************************************************************************
nav ods module header
All rights reserved I-NAV 2023 2033
***********************************************************************************/
/***********************************************************************************
Modification history

|-----------+---------------+-------------------|
|Author          |    Date               |    Done                      |
|-----------+---------------+-------------------|
|DengWei       |  2023-2-4          | First Creation             |
|-----------+---------------+-------------------|  
***********************************************************************************/
#ifndef __NAV_OD_H__
#define __NAV_OD_H__
//#include "NAV_MCU.h"
#include "nav_type.h"
#include "algorithm.h"

void Get_ODS_Data(_NAV_Data_Full_t* NAV_Data_Full_p,CombineDataTypeDef* CombineData_p);
void ODS_Angle_Estimation(_NAV_Data_Full_t* NAV_Data_Full_p);
double WheelSpeedOptimize(double v0,double v1,double v2,double v3,double scale_factor,unsigned char *pSpeed_valid_flag);
void Scalar_KalmanFilte(double * x,double zk,double* P,double Q,double R,double MaxError);

#endif
