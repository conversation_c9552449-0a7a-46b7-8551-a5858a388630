/***********************************************************************************
This file include all header file and enum parameters 
All rights reserved I-NAV 2023 2033
***********************************************************************************/
/***********************************************************************************
Modification history

|-----------+---------------+-------------------|
|Author          |    Date               |    Done                      |
|-----------+---------------+-------------------|
|DengWei       |  2023-2-4          | First Creation             |
|-----------+---------------+-------------------|  
***********************************************************************************/
#ifndef __NAV_INCLUDEES_H__
#define __NAV_INCLUDEES_H__

#include <string.h>
#include <stdio.h>

//#include "NAV_MCU.h"
#include "math.h"
//#include "arm_math.h"

#include "nav_type.h"

#include "nav_math.h"
#include "nav_const.h"
#include "nav_sins.h"
#include "nav_ods.h"
#include "nav_kf.h"
#include "nav_app.h" 
#include "nav.h"
#include "navlog.h"
#include "algorithm.h"
#include "nav_magnet.h"
#include "nav_mahony.h"
#include "nav_cli.h"
#include "nav_imu.h"

#ifdef linux
#include <sys/time.h>
#endif


#include <Windows.h>

#ifdef _DEBUG
    #define printf(...) do { \
        char buffer[256]; \
        sprintf(buffer, __VA_ARGS__); \
        OutputDebugStringA(buffer); \
    } while (0)
#endif


/**********************************************************************************
V:版本
X1：算法
001：客户
B:扩展版本
01:具体版本号，依次累加
R:发行版本号
01：具体版本号，依次累加
D001：还在调试过程中时，需要再版本号后面加后缀D001，出版本时依次累加
************************************************************************************/
#define SINS_ALGORITHM_BUILD_VERSION  "VX1.001.B01R01.D001"



#define  RETURN_SUCESS			1
#define  RETURN_FAIL 			0

//角速度量程度/s
#define MAX_GYRO_VALUE         500

//加速度量程m/s2
#define MAX_ACC_VALUE          (6*G0)
//最大杆臂单位m
#define MAX_ARM_VALUE          20

//静止时候最大速度随机抖动m/s2
#define MAX_STATIC_VN          0.05       


//判断转弯最小 °/s
#define MIN_TURN_GYRO_VALUE    2.0f

#define ALLIGN_DIFF_ACC_USE   1.5//初始校准时候加计时间单差范围m/s2

#ifndef NULL
#define NULL 0
#endif
//静态校验最大计数值，用于静态平均角速度、加速度估计
#define MAX_STATIC_STORED_COUNT  100//200

//IMU奇异值检测门限
#define GYRO_ABNORMAL_THRESHOLD  100//unit:degree/s
#define ACC_ABNORMAL_THRESHOLD   20//unit:m/s2


//应用场景设置
enum EUSECASES 
{
    E_USE_CASE_In_Vehicle =0,       			//车载模式
    E_USE_CASE_In_UAV=1,						//无人机模式
};



//算法类型
enum EMETHODTYPE 
{
    E_METHOD_TYPE_KALMAN =0,       				//保持kalman滤波
    E_METHOD_TYPE_DR=1,							//直接速度推算
};

//是否仿真调试模式
enum ESIMMODE
{
	 E_SIM_MODE_NORMAL =0,       				//0:仿真模式为正常运行
	 E_SIM_MODE_DEBUG=1,		   					//1:仿真模式为调试模式
};


enum EIMUSELECT 
{
    E_IMU_ATT_MEMS =0,       					//mems
    E_IMU_ATT_IFOG=1,							//ifog
};

enum EIMUMANUNAME
{
    E_IMU_MANU_460 =0,       					//imu460
    E_IMU_MANU_SCHA63X=1,						//scha63x
    E_IMU_MANU_ADIS16465=2,						//adis16465
    E_IMU_MANU_EPSON_G370=3,					//epson7370
};



enum ELONLATHEMISPHERE
{
    E_LON_EAST_Hemisphere 	='E', 				//东经     	
    E_LON_WEST_Hemisphere 	='W',				//西经			
    E_LAT_NORTH_Hemisphere 	='N',				//北纬       	
    E_LAT_SOUTH_Hemisphere 	='S',				//南纬
};


//gps定位状态
enum EGPSPOSSTATUS
{
    E_GPS_POS_VALID =0x41,       				//有效'A'D
	   E_GPS_POS_VALID_RTK =0x44,       				//有效'A'D
    E_GPS_POS_INVALID=0x56,		 				// 无效'V'
};

//组合导航算法中，支持的GNSS状态
enum ENAVSUPPORTGNSSSATUS
{
    E_NAV_SUPPORT_RTK_FIEX_STATUS=0,			//仅仅支持对rtk固定解的约束  
	E_NAV_SUPPORT_RTK_ALL_STATUS=1, 			//支持SPP、DGPS、FIEXED、FLOAT解的约束
};

//rtk状态
enum EGPSRTKSTATUS
{
    E_GPS_RTK_INVALID=0, 						//0: 无定位
	E_GPS_RTK_SPP=1,							//1：单点定位  		
    E_GPS_RTK_DGPS=2,							//2：差分解
    E_GPS_RTK_FIXED=4,							//4：RTK固定解
    E_GPS_RTK_FLOAT=5,							//5：RTK浮动解
    E_GPS_RTK_TOTAL=6,
    
};

//汽车档位状态
//2向前，4：向后
enum EODSGEARSTATUS
{
	E_ODS_GEAR_STATIC=0,  
    E_ODS_GEAR_FORWARD=2,  
	E_ODS_GEAR_BACKWARD=4, 
};

enum ENAVSTATUS
{
    E_NAV_STATUS_START =0,       	        //开始导航加载导航参数
    E_NAV_STATUS_ROUTH_ALIGN=1,		    	//SINS初对准
    E_NAV_STATUS_SINS_KF_INITIAL=2,      	//SINS、KF初始化
    E_NAV_STATUS_SYSTEM_STANDARD=3,      	//系统标定
    E_NAV_STATUS_IN_NAV=4,                 	//正常导航
    E_NAV_STATUS_STOP=5,                   	//停止导航
    E_NAV_STATUS_TOTAL=6,					//总共状态数目
};

//GPS数据更新状态
enum EGPSUPDATESTATUS
{
	E_GPS_NO_UPDATE=0,						//GPS数据未更新
	E_GPS_IS_UPDATE=1,						//GPS数据更新
};

//融合定位状态
enum EFUSHIONSTATUS
{
	E_FUNSION_NONE=0,						//无效融合
	E_FUNSION_GPS=1,						//GPS融合
	E_FUNSION_WHEEL=2,						//用wheel融合
	E_FUNSION_MOTION=3,						//运动模型约束
	E_FUNSION_TOTAL=4,
};

//是否有ODS轮速
enum EODSWHEELFLAGSTATUS
{
	E_ODS_WHEEL_FLAG_NONE=0,				//没有ODS轮速
	E_ODS_WHEEL_FLAG_HAVE=1,				//有ODS轮速
};

//量测更新标志
enum EKALMANMEASUREUPDATE
{
	E_KALMAN_MEASURE_UPDATE_NONE=0,			//不进行量测更新
	E_KALMAN_MEASURE_UPDATE_HAVE=1,			//进行量测更新
};

//量测更新是否有航向约束
enum EKALMANMEASUREHEADING
{
	E_KALMAN_MEASURE_HEADING_NO=0,			//不进行航向量测更新
	E_KALMAN_MEASURE_HEADING_YES=1,			//进行航向量测更新
};

//量测更新是否有速度约束
enum EKALMANMEASUREVEL
{
	E_KALMAN_MEASURE_VEL_NO=0,				//不进行速度量测更新
	E_KALMAN_MEASURE_VEL_YES=1,				//进行速度量测更新
};

//量测更新是否有位置约束
enum EKALMANMEASUREPOS
{
	E_KALMAN_MEASURE_POS_NO=0,				//不进行位置量测更新
	E_KALMAN_MEASURE_POS_YES=1,				//进行位置量测更新
};




//卡尔曼滤波更新步骤
#define KLMAN_FILTER_SETP_SUM        8//6		//卡尔曼滤波步骤总数
enum EKALMANFILTERSTEP
{
	E_KALMAN_FILTER_NONE=0,					//尚未开始kalman滤波
	E_KALMAN_TIME_UPDATE_1=1,				//卡尔曼滤波时间更新1
	E_KALMAN_TIME_UPDATE_2=2,				//卡尔曼滤波时间更新2
	E_KALMAN_TIME_UPDATE_3=3,				//卡尔曼滤波时间更新3
	E_KALMAN_TIME_UPDATE_4=4,				//卡尔曼滤波时间更新4
	E_KALMAN_MEASURE_UPDATE_1=5,			//卡尔曼滤波量测更新1
	E_KALMAN_MEASURE_UPDATE_2=6,			//卡尔曼滤波量测更新2
    E_KALMAN_MEASURE_UPDATE_3 = 7,			//卡尔曼滤波量测更新3
    E_KALMAN_MEASURE_UPDATE_4 = 8,			//卡尔曼滤波量测更新4
};

enum ENAVSTANDARDFLAGSTATUS
{
	E_NAV_STANDARD_NO_PROCCSS=0,			//0:未标定完成 
	E_NAV_STANDARD_PROCCSSING=1,			//1：标定中
	E_NAV_STANDARD_PROCCSSED=2,				//2：标定完成
	E_NAV_STANDARD_TOTAL=3,					//总共状态数目
};

typedef struct {    
    int flag;    //0单个文件，1多个文件
	char obsDir[1024];	          //观测文件目录            /*观测文件路径 obs file path */
    char config[1024];//配置文件
	char*inf[20];//输入文件
	char*outf1[20];//输出文件1
	char*outf2[20];//输出文件2
    char*outf3[20];//输出文件3
	char*outf4[20];//输出文件4
	char*outf5[20];//输出文件5
    int filecount;

} procparam_t;
//消息统计字
static char* g_NavStatusStaTxt[E_NAV_STATUS_TOTAL] =
{
    "E_NAV_STATUS_START" ,      	        //开始导航加载导航参数
    "E_NAV_STATUS_ROUTH_ALIGN",		    	//SINS初对准
    "E_NAV_STATUS_SINS_KF_INITIAL",      	//SINS、KF初始化
    "E_NAV_STATUS_SYSTEM_STANDARD",      	//系统标定
    "E_NAV_STATUS_IN_NAV",                  //正常导航
    "E_NAV_STATUS_STOP"                   	//停止导航
};

static char* g_NavStandardStaTxt[E_NAV_STANDARD_TOTAL] =
{
    "E_NAV_STANDARD_NO_PROCCSS" ,      	    //0:未标定完成 
    "E_NAV_STANDARD_PROCCSSING",			//1：标定中
    "E_NAV_STANDARD_PROCCSSED",     		//2：标定完成
};

static char* g_NavFusionSourceStaTxt[E_FUNSION_TOTAL] =
{
    "E_FUNSION_NONE",						//无效融合
	"E_FUNSION_GPS",						//GPS融合
	"FUNSION_WHEEL",						//用wheel融合
	"E_FUNSION_MOTION",						//运动模型约束
};

static char* g_NavRtkStatusStaTxt[E_GPS_RTK_TOTAL] =
{
    "E_GPS_RTK_INVALID",  
	"E_GPS_RTK_SPP", 
    "E_GPS_RTK_DGPS",
    "E_GPS_RTK_UNKNOW",
    "E_GPS_RTK_FIXED",
    "E_GPS_RTK_FLOAT",
};

extern double R_heading;//***********另一个文件引用*****
extern _NAV_Data_Full_t NAV_Data_Full;
//extern int g_KF_UP2_gps_delay_cunt;
extern _NAV_Funsion_Status_Time_t g_NAV_Funsion_Status_Time;
extern unsigned long int g_NavIndex;
extern int time_delay;
//extern double gps_pre_location[2];
//extern int timecount;

extern void Get_GNSS_Data(_NAV_Data_Full_t* NAV_Data_Full_p,CombineDataTypeDef* CombineData_p);
extern unsigned char is_gnss_update(_NAV_Data_Full_t* NAV_Data_Full_p);
extern void SINS_UP_ATT(_NAV_Data_Full_t* NAV_Data_Full_p,double *phim);

#endif

