/***********************************************************************************
Matrix operation module
All rights reserved I-NAV 2023 2033
***********************************************************************************/
/***********************************************************************************
Modification history

|-----------+---------------+-------------------|
|Author          |    Date               |    Done                      |
|-----------+---------------+-------------------|
|DengWei       |  2023-2-4          | First Creation             |
|-----------+---------------+-------------------|  
***********************************************************************************/

#ifndef _CRT_SECURE_NO_WARNINGS
#define _CRT_SECURE_NO_WARNINGS
#endif


#include "NAV_Includes.h"

#include <stdlib.h>
#include "math.h"
#include "string.h"

//---------------------------------------------------------------------------------------------
#define F1									(   2.0 * 1)						// 2
#define F2									((F1)*2 * 2)						// 8
#define F3									((F2)*2 * 3)						// 48
#define F4									((F3)*2 * 4)						// 384
#define F5									((F4)*2 * 5)						// 3840
/****************************************************************************************/

double  glLudMat_vv[MAXMatrixSize] = {0}; 
double	m_InvMat_B[MAXMatrixSize*MAXMatrixSize]={0};		
int		m_InvMat_indx[MAXMatrixSize]={0};	
char	g_MatrixBuff[1024*4]={0};

/*********************************common function*****************************************/
union 
{
	char  			bd[8];
	unsigned short  iv;
	short 			sv;
    int   			lv;
	unsigned int 	uv;
	float      		fv;
	double     		dv;
}m_uMemory;

unsigned short get_Ushort(char *msgbuff, int i)
{
	m_uMemory.bd[0] = msgbuff[i];
	m_uMemory.bd[1] = msgbuff[i+1];
	return m_uMemory.iv;
}  

float get_F32(char *msgbuff,int i)
{	
	m_uMemory.bd[0] = msgbuff[i];
	m_uMemory.bd[1] = msgbuff[i+1];
	m_uMemory.bd[2] = msgbuff[i+2];
	m_uMemory.bd[3] = msgbuff[i+3];    
	return m_uMemory.fv;
}

unsigned int get_UInt32(char *msgbuff,int i)
{
	m_uMemory.bd[0] = msgbuff[i];
	m_uMemory.bd[1] = msgbuff[i+1];
	m_uMemory.bd[2] = msgbuff[i+2];
	m_uMemory.bd[3] = msgbuff[i+3];    
	return m_uMemory.uv;
}

int get_Int32(char *msgbuff, int i)
{
	m_uMemory.bd[0] = msgbuff[i];
	m_uMemory.bd[1] = msgbuff[i+1];
	m_uMemory.bd[2] = msgbuff[i+2];
	m_uMemory.bd[3] = msgbuff[i+3];    
	return m_uMemory.lv;
} 

double get_D64(char *msgbuff, int i)
{	
    m_uMemory.bd[0] = msgbuff[i];
    m_uMemory.bd[1] = msgbuff[i+1];
    m_uMemory.bd[2] = msgbuff[i+2];
    m_uMemory.bd[3] = msgbuff[i+3];
    m_uMemory.bd[4] = msgbuff[i+4];
    m_uMemory.bd[5] = msgbuff[i+5];
    m_uMemory.bd[6] = msgbuff[i+6];
    m_uMemory.bd[7] = msgbuff[i+7];
    return m_uMemory.dv;
}

void BDAddFieldu2(char *Buffer, unsigned short *Index, const unsigned short DataToAdd, char endianness)
{
	unsigned int iloop;
    unsigned char *pAddr = (unsigned char *)&DataToAdd;
    
    if(endianness == FirstSend_LowByte)  
    {
        for(iloop = 0; iloop < 2; iloop++)
            Buffer[(*Index)++] = *pAddr++;
    }
    else
    {
        Buffer[(*Index)++] = (DataToAdd>>8) & 0xFF;
        Buffer[(*Index)++] = DataToAdd & 0xFF;
    }
}

void BDAddFieldInt16(char *Buffer, unsigned short *Index, const short DataToAdd, char endianness)
{
    if(endianness == FirstSend_LowByte)  
    {
        //low
        Buffer[(*Index)++] = DataToAdd & 0xFF; 
        Buffer[(*Index)++] = (DataToAdd>>8) & 0xFF;   
    }
    else
    {
        //High
        Buffer[(*Index)++] = (DataToAdd>>8) & 0xFF;
        Buffer[(*Index)++] = DataToAdd & 0xFF;
    }
}

void BDAddFieldi4(char *Buffer, unsigned short *Index, const int DataToAdd, char endianness)
{
    if(endianness == FirstSend_LowByte)  
    {
        //low
        Buffer[(*Index)++] = DataToAdd & 0xFF;
        Buffer[(*Index)++] = (DataToAdd>>8) & 0xFF;
        Buffer[(*Index)++] = (DataToAdd>>16) & 0xFF;
        Buffer[(*Index)++] = (DataToAdd>>24) & 0xFF;    
    }
    else
    {
        //High
        Buffer[(*Index)++] = (DataToAdd>>24) & 0xFF;
        Buffer[(*Index)++] = (DataToAdd>>16) & 0xFF;
        Buffer[(*Index)++] = (DataToAdd>>8) & 0xFF;    
        Buffer[(*Index)++] = DataToAdd & 0xFF;
    }
}

void BDAddFieldInt32(char *Buffer, unsigned short *Index, const int DataToAdd, char endianness)
{
    if(endianness == FirstSend_LowByte)  
    {
        //low
        Buffer[(*Index)++] = DataToAdd & 0xFF;
        Buffer[(*Index)++] = (DataToAdd>>8) & 0xFF;
        Buffer[(*Index)++] = (DataToAdd>>16) & 0xFF;
        Buffer[(*Index)++] = (DataToAdd>>24) & 0xFF;    
    }
    else
    {
        //High
        Buffer[(*Index)++] = (DataToAdd>>24) & 0xFF;
        Buffer[(*Index)++] = (DataToAdd>>16) & 0xFF;
        Buffer[(*Index)++] = (DataToAdd>>8) & 0xFF;    
        Buffer[(*Index)++] = DataToAdd & 0xFF;
    }
}

void BDAddFieldUInt32(char *Buffer, unsigned short *Index, const unsigned int DataToAdd, char endianness)
{
    if(endianness == FirstSend_LowByte)  
    {
        //low
        Buffer[(*Index)++] = DataToAdd & 0xFF;
        Buffer[(*Index)++] = (DataToAdd>>8) & 0xFF;
        Buffer[(*Index)++] = (DataToAdd>>16) & 0xFF;
        Buffer[(*Index)++] = (DataToAdd>>24) & 0xFF;    
    }
    else
    {
        //High
        Buffer[(*Index)++] = (DataToAdd>>24) & 0xFF;
        Buffer[(*Index)++] = (DataToAdd>>16) & 0xFF;
        Buffer[(*Index)++] = (DataToAdd>>8) & 0xFF;    
        Buffer[(*Index)++] = DataToAdd & 0xFF;
    }
}

void BDAddFieldFloat(char *Buffer, unsigned short *Index, const float DataToAdd, char endianness)
{
    unsigned short iloop;
    unsigned char *pAddr;
    
    if(endianness == FirstSend_LowByte)  
    {
        //low
        pAddr = (unsigned char *)&DataToAdd;
        for(iloop = 0; iloop < 4; iloop++)
        {
            Buffer[(*Index)++] = *pAddr++;
        }
    }
    else
    {
        //High
        pAddr = (unsigned char *)&DataToAdd + 3;
        for(iloop = 0; iloop < 4; iloop++)
        {
            Buffer[(*Index)++] = *pAddr--;
        }
    }
}

//double数据存入buff
void BDAddFieldDouble(char *Buffer, unsigned short *Index, const double DataToAdd, char endianness)
{
    unsigned short iloop;
    unsigned char *pAddr;
    
    if(endianness == FirstSend_LowByte)  
    {
        //low
        pAddr = (unsigned char *)&DataToAdd;
        for(iloop = 0; iloop < 8; iloop++)
            Buffer[(*Index)++] = *pAddr++;
    }
    else
    {
        //high
        pAddr = (unsigned char *)&DataToAdd + 7;
        for(iloop = 0; iloop < 8; iloop++)
            Buffer[(*Index)++] = *pAddr--;
    }
}

/* CalculateCheckSum ---------------------------------------------------------------
* args   : char* pBuffer    			I   character string
*          unsigned short size         	I   size of character string
* return : checksum
*-----------------------------------------------------------------------------*/
char CalculateCheckSum(char* pBuffer,unsigned short size)
{
	char checksum = 0;
	
	while (size--)
	{
		checksum += *pBuffer++;
	}
	checksum = ~checksum;
	
	return checksum|0x30;//checksum;
}
void GetDoubleParmsFormString(char * buff, char * splitchar, unsigned int maxnum,double *data)
{
	char *mstr = NULL;
	unsigned char index=0;
	if(NULL==buff || NULL==splitchar || NULL==data)
	{
		//printf("ERROR:parse_split_int is NULL\r\n");
		return;
	}
	mstr = strtok(buff, splitchar);
	if(NULL!=mstr)
	{
		data[index]=atof(mstr);
		index++;
	}
	while(mstr)
	{
		 if(index>=maxnum)
		 {
			return;
		 }
		 mstr = strtok(NULL, splitchar);
		 if(NULL!=mstr)
		 {
		 	data[index]=atof(mstr);
			index++; 	
		 }
	}
}

/* parse_split_fnum ---------------------------------------------------------------
* args   : 	char* buff    					I   character string
*          	char * splitchar        		I   分割字符串
			unsigned int maxnum				I	最大的分割数目
			float *data						O	分割后字符串转浮点数保存
			unsigned int * num				O	实际分割的数目<=maxnum
*-----------------------------------------------------------------------------*/
void parse_split_fnum(char * buff, char * splitchar, unsigned int maxnum,float *data, unsigned int * num)
{
	char *mstr = NULL;
	unsigned char index=0;
	if(NULL==buff || NULL==splitchar || NULL==data || NULL==num )
	{
		//printf("ERROR:parse_split_int is NULL\r\n");
		return;
	}
	mstr = strtok(buff, splitchar);
	if(NULL!=mstr)
	{
		data[index]= (float)atof(mstr);
		index++;
	}
	while(mstr)
	{
		 if(index>=maxnum)
		 {
		 	*num=index;
			return;
		 }
		 mstr = strtok(NULL, splitchar);
		 if(NULL!=mstr)
		 {
		 	data[index]= (float)atof(mstr);
			index++; 	
		 }
	}
	*num=index;
}


//取出当前浮点数据中小数部分
double GetFracFromDouble(double m)
{
	int m_int = (int)m;
	return m-m_int;
}

/*********************************math*****************************************/
void EyeMatrix(double *pMat, int n, double dValue)
{
	int i=0, j=0;

	for (i=0; i<n; i++) 
	{
		for (j=0; j<n; j++)
		{
			if (i==j)
			{
				pMat[i*n+j]=dValue;
			}
			else
			{
				pMat[i*n+j]=0.0;
			}
		}
	}
}

/* inner product ---------------------------------------------------------------
* inner product of vectors
* args   : double *a,*b     I   vector a,b (n x 1)
*          int    n         I   size of vector a,b
* return : a'*b
*-----------------------------------------------------------------------------*/
double InnerDot(const double *a, const double *b, int n)
{
    double c=0.0;
    
    while (--n>=0) 
	{
		c+=a[n]*b[n];
	}

    return c;
}

/* euclid norm -----------------------------------------------------------------
* euclid norm of vector
* args   : double *a        I   vector a (n x 1)
*          int    n         I   size of vector a
* return : || a ||
*-----------------------------------------------------------------------------*/
double norm(const double *a, int n)
{
    return sqrt(InnerDot(a, a, n));
}

/* outer product of 3d vectors -------------------------------------------------
* outer product of 3d vectors 
* args   : double *a,*b     I   vector a,b (3 x 1)
*          double *c        O   outer product (a x b) (3 x 1)
* return : none
*-----------------------------------------------------------------------------*/
void cross3(const double *a, const double *b, double *c)
{
    c[0] = a[1]*b[2]-a[2]*b[1];
    c[1] = a[2]*b[0]-a[0]*b[2];
    c[2] = a[0]*b[1]-a[1]*b[0];
}

/* normalize 3d vector ---------------------------------------------------------
* normalize 3d vector
* args   : double *a        I   vector a (3 x 1)
*          double *b        O   normlized vector (3 x 1) || b || = 1
* return : status (1:ok,0:error)
*-----------------------------------------------------------------------------*/
int normv3(const double *a, double *b)
{
    double r;
    if ((r=norm(a,3))<=0.0) return 0;
    b[0]=a[0]/r;
    b[1]=a[1]/r;
    b[2]=a[2]/r;
    return 1;
}

/* copy matrix -----------------------------------------------------------------
* copy matrix
* args   : double *A        O   destination matrix A (n x m)
*          double *B        I   source matrix B (n x m)
*          int    n,m       I   number of rows and columns of matrix
* return : none
*-----------------------------------------------------------------------------*/
void matcpy(double *A, const double *B, int n, int m)
{
    memcpy(A,B,sizeof(double)*n*m);
}
/* matrix routines -----------------------------------------------------------*/

/* multiply matrix (wrapper of blas dgemm) -------------------------------------
* multiply matrix by matrix (C=alpha*A*B+beta*C)
* args   : char   *tr       I  transpose flags ("N":normal,"T":transpose)
*          int    n,k,m     I  size of (transposed) matrix A,B
*          double alpha     I  alpha
*          double *A,*B     I  (transposed) matrix A (n x m), B (m x k)
*          double beta      I  beta
*          double *C        IO matrix C (n x k)
* return : none
*-----------------------------------------------------------------------------*/
void matmul(const char *tr, int n, int k, int m, double alpha, const double *A, const double *B, double beta, double *C)
{
    double d;
    int i,j,x,f=tr[0]=='N'?(tr[1]=='N'?1:2):(tr[1]=='N'?3:4);
    
	for (i=0;i<n;i++)
	{ 
		for (j=0;j<k;j++) 
		{
			d=0.0;

			switch (f) 
			{
				case 1: 
					{
						for (x=0;x<m;x++) 
						{
							if(0!=A[i+x*n] && 0!=B[x+j*m])//减少不必要浮点运算
							{
								d+=A[i+x*n]*B[x+j*m];
							}
						}

						break;
					}
				case 2:
					{
						for (x=0;x<m;x++) 
						{
							if(0!=A[i+x*n] && 0!=B[x+j*k])
							{
								d+=A[i+x*n]*B[j+x*k]; 
							}
						}
						
						break;
					}
				case 3: 
					{
						for (x=0;x<m;x++) 
						{
							if(0!=A[x+i*m] && 0!=B[x+j*m])
							{
								d+=A[x+i*m]*B[x+j*m]; 
							}
						}
						
						break;
					}
				case 4: 
					{
						for (x=0;x<m;x++) 
						{
							if(0!=A[x+i*m] && 0!=B[j+x*k])
							{
								d+=A[x+i*m]*B[j+x*k]; 
							}
						}
						
						break;
					}
			}

			if(1.0 == alpha && beta==0.0)//减少不必要浮点运算
			{
				C[i+j*n]=d; 
			}
			else if(-1.0 == alpha && beta==0.0)//减少不必要浮点运算
			{
				C[i+j*n]=-d; 
			}
			else if (beta==0.0) 
			{
				C[i+j*n]=alpha*d; 
			}
			else 
			{
				C[i+j*n]=alpha*d+beta*C[i+j*n];
			}
		}
	}
}

//矩阵求转置
void Mat_Tr(int n,const double *A,double *A_T)
{
    int i,j;    
	for (i=0;i<n;i++)
	{ 
		for (j=0;j<n;j++) 
		{
			A_T[i+j*n] = A[j+i*n];
		}
	}
}
/* LU decomposition ----------------------------------------------------------*/
int ludcmp(double *A, int n, int *indx, double *d)
{
    double big,s,tmp;
    int i,imax=0,j,k;

	memset(glLudMat_vv, 0, sizeof(glLudMat_vv));
    
    *d=1.0;

    for (i=0;i<n;i++) 
	{
        big=0.0; 
		
		for (j=0;j<n;j++) 
		{
			if ((tmp=fabs(A[i+j*n]))>big) 
			{
				big=tmp;
			}
		}

        if (big>0.0) 
		{
			glLudMat_vv[i]=1.0/big; 
		}
		else 
		{
			memset(glLudMat_vv, 0, sizeof(glLudMat_vv));
			return -1;
		}
    }

    for (j=0;j<n;j++) 
	{
        for (i=0;i<j;i++) 
		{
            s=A[i+j*n]; 
			
			for (k=0;k<i;k++) 
			{
				s-=A[i+k*n]*A[k+j*n];
			}
			
			A[i+j*n]=s;
        }

        big=0.0;

        for (i=j;i<n;i++) 
		{
            s=A[i+j*n];
			
			for (k=0;k<j;k++) 
			{
				s-=A[i+k*n]*A[k+j*n];
			}
			
			A[i+j*n]=s;

            if ((tmp=glLudMat_vv[i]*fabs(s))>=big) 
			{
				big=tmp; 
				imax=i;
			}
        }

        if (j!=imax) 
		{
            for (k=0;k<n;k++) 
			{
                tmp=A[imax+k*n]; 
				A[imax+k*n]=A[j+k*n]; 
				A[j+k*n]=tmp;
            }

            *d=-(*d); 
			glLudMat_vv[imax]=glLudMat_vv[j];
        }

        indx[j]=imax;

        if (A[j+j*n]==0.0) 
		{
			memset(glLudMat_vv, 0, sizeof(glLudMat_vv));
			return -1;
		}

        if (j!=n-1)
		{
            tmp=1.0/A[j+j*n];
			
			for (i=j+1;i<n;i++)
			{
				A[i+j*n]*=tmp;
			}
        }
    }

    return 0;
}

/* LU back-substitution ------------------------------------------------------*/
void lubksb(const double *A, int n, const int *indx, double *b)
{
    double s;
    int i,ii=-1,ip,j;
    
    for (i=0;i<n;i++) 
	{
        ip=indx[i]; 
		s=b[ip];
		b[ip]=b[i];

        if (ii>=0) 
		{
			for (j=ii;j<i;j++) 
			{
				s-=A[i+j*n]*b[j]; 
			}
		}
		else if (s) 
		{
			ii=i;
		}

        b[i]=s;
    }

    for (i=n-1;i>=0;i--) 
	{
        s=b[i]; 
		
		for (j=i+1;j<n;j++)
		{
			s-=A[i+j*n]*b[j]; 
		}
		
		b[i]=s/A[i+i*n];
    }
}

/* inverse of matrix ---------------------------------------------------------*/								 
int matinv(double *A, int n)
{
    double d=0.0;
    int i=0, j=0;

	if(n > MAXMatrixSize)
	{
		return -1;
	}
	memset(m_InvMat_indx, 0, sizeof(m_InvMat_indx));

	memset(m_InvMat_B, 0, sizeof(m_InvMat_B));
	matcpy(m_InvMat_B,A,n,n);

    if (ludcmp(m_InvMat_B,n,m_InvMat_indx,&d)) 
	{ 
		memset(m_InvMat_indx, 0, sizeof(m_InvMat_indx));
		memset(m_InvMat_B, 0, sizeof(m_InvMat_B));
		return -1;
	}

    for (j=0;j<n;j++)
	{
        for (i=0;i<n;i++) 
		{
			A[i+j*n]=0.0; 
		}

		A[j+j*n]=1.0;

        lubksb(m_InvMat_B,n,m_InvMat_indx,A+j*n);
    }

    return 0;
}
double test_Inv1,test_Inv2;
void Mat2_Inv(double* Mat2temp, double *Mat2temp_Inc)
{
	double demp,demp2;
	demp = Mat2temp[0]*Mat2temp[3]-Mat2temp[1]*Mat2temp[2];
			
	if ( demp == 0)
	{
		demp = 0.0000000000000001;
	}
	demp2 = 1/demp;
	test_Inv1 = demp;
	test_Inv2= demp2;
	Mat2temp_Inc[0] =  Mat2temp[3]*demp2;
	Mat2temp_Inc[1] = -Mat2temp[2]*demp2;
	Mat2temp_Inc[2] = -Mat2temp[1]*demp2;
	Mat2temp_Inc[3] =  Mat2temp[0]*demp2;
}

void Mat3_Inv(double* Mat3temp, double *Mat3temp_Inc)
{
	double demp,demp2;
	demp =    Mat3temp[0]*(Mat3temp[4]*Mat3temp[8]-Mat3temp[5]*Mat3temp[7])
			- Mat3temp[1]*(Mat3temp[3]*Mat3temp[8]-Mat3temp[5]*Mat3temp[6])
			+ Mat3temp[2]*(Mat3temp[3]*Mat3temp[7]-Mat3temp[4]*Mat3temp[6]);
	if ( demp == 0)
	{
		demp = 1e-34;
	}
	demp2 = 1/demp;
	test_Inv1 = demp;
	test_Inv2= demp2;
	Mat3temp_Inc[0] =  (Mat3temp[4]*Mat3temp[8]-Mat3temp[7]*Mat3temp[5])*demp2;
	Mat3temp_Inc[1] = -(Mat3temp[1]*Mat3temp[8]-Mat3temp[7]*Mat3temp[2])*demp2;
	Mat3temp_Inc[2] =  (Mat3temp[1]*Mat3temp[5]-Mat3temp[4]*Mat3temp[2])*demp2;
	Mat3temp_Inc[3] = -(Mat3temp[3]*Mat3temp[8]-Mat3temp[6]*Mat3temp[5])*demp2;
	Mat3temp_Inc[4] =  (Mat3temp[0]*Mat3temp[8]-Mat3temp[6]*Mat3temp[2])*demp2;
	Mat3temp_Inc[5] = -(Mat3temp[0]*Mat3temp[5]-Mat3temp[3]*Mat3temp[2])*demp2;
	Mat3temp_Inc[6] =  (Mat3temp[3]*Mat3temp[7]-Mat3temp[6]*Mat3temp[4])*demp2;
	Mat3temp_Inc[7] = -(Mat3temp[0]*Mat3temp[7]-Mat3temp[6]*Mat3temp[1])*demp2;
	Mat3temp_Inc[8] =  (Mat3temp[0]*Mat3temp[4]-Mat3temp[3]*Mat3temp[1])*demp2;
}
/*----------------- -----------------------------------------------------------
* args   : 
*          double *Cnb      I   Cnb(3*3)
*          double *qnb0    IO  qnb0 (4 x 1)
* return : none
*-----------------------------------------------------------------------------*/
void m2qnb(double* Cnb, double* qnb0)// 变换矩阵计算四元数
{
	int i;
	double C11, C12, C13, C21, C22, C23, C31, C32, C33;
	double q0t,q1t,q2t,q3t;
	//姿态转换矩阵
	C11 = Cnb[0]; C12 = Cnb[3]; C13 = Cnb[6];
	C21 = Cnb[1]; C22 = Cnb[4]; C23 = Cnb[7];
	C31 = Cnb[2]; C32 = Cnb[5]; C33 = Cnb[8];

	q0t = 0.5 * sqrt(1 + C11 + C22 + C33);
	q1t = 0.5 * sqrt(1 + C11 - C22 - C33);
	q2t = 0.5 * sqrt(1 - C11 + C22 - C33);
	q3t = 0.5 * sqrt(1 - C11 - C22 + C33);
	if (C11 >= C22 + C33) {
		qnb0[1] = q1t;
		qnb0[0] = (C32 - C23) / (4 * q1t);
		qnb0[2] = (C12 + C21) / (4 * q1t);
		qnb0[3] = (C13 + C31) / (4 * q1t);
	}
	else if (C22 >= C11 + C33) {
		qnb0[2] = q2t;
		qnb0[0] = (C13 - C31) / (4 * q2t);
		qnb0[1] = (C12 + C21) / (4 * q2t);
		qnb0[3] = (C23 + C32) / (4 * q2t);
	}
	else if (C33 >= C11 + C22) {
		qnb0[3] = q3t;
		qnb0[0] = (C21 - C12) / (4 * q3t);
		qnb0[1] = (C13 + C31) / (4 * q3t);
		qnb0[2] = (C23 + C32) / (4 * q3t);
	}
	else {
		qnb0[0] = q0t;
		qnb0[1] = (C32 - C23) / (4 * q0t);
		qnb0[2] = (C13 - C31) / (4 * q0t);
		qnb0[3] = (C21 - C12) / (4 * q0t);
	}
	for (i = 0; i < 4; i++)// 单位化
	{
		qnb0[i] /= norm(qnb0, 4);
	}
}

/*----------------- -----------------------------------------------------------
* args   : 
*          double *qnb0    I matrix qnb0 (4 x 1)
*          double *Cnb     IO   Cnb(3*3)
* return : none
*-----------------------------------------------------------------------------*/
void Qnb2Cnb(double* qnb, double* Cnb)  //四元数转换矩阵
{
	double q11 = qnb[0] * qnb[0], q12 = qnb[0] * qnb[1], q13 = qnb[0] * qnb[2], q14 = qnb[0] * qnb[3];
	double q22 = qnb[1] * qnb[1], q23 = qnb[1] * qnb[2], q24 = qnb[1] * qnb[3];
	double q33 = qnb[2] * qnb[2], q34 = qnb[2] * qnb[3];
	double q44 = qnb[3] * qnb[3];
	Cnb[0] = q11 + q22 - q33 - q44; Cnb[3] = 2 * (q23 - q14);		Cnb[6] = 2 * (q24 + q13);
	Cnb[1] = 2 * (q23 + q14);		Cnb[4] = q11 - q22 + q33 - q44;	Cnb[7] = 2 * (q34 - q12);
	Cnb[2] = 2 * (q24 - q13);		Cnb[5] = 2 * (q34 + q12);		Cnb[8] = q11 - q22 - q33 + q44;
}
/******************************************************************************
*原  型：void Get_Param_Data(Param_Data_t* Param_Data,CombineDataTypeDef Sensors_Data_Rew)
*功  能：获取配置参数
*输  入：无
*输  出：无
*******************************************************************************/
void Cnb2att(double* Cnb, double* att)  //转移矩阵转姿态角
{	
	double temp=0.0;
	temp = Cnb[2]*Cnb[2]+Cnb[2+3*2]*Cnb[2+3*2];
	att[0] = atan2(Cnb[2+3*1],sqrt(temp))*RAD2DEG;
	att[1] = atan2(-Cnb[2],Cnb[2+3*2])*RAD2DEG;
	att[2] = atan2(-Cnb[0+3*1],Cnb[1+3*1])*RAD2DEG;
}
/*----------------- -----------------------------------------------------------
* args   : 
*          double *qnb0    I matrix qnb0 (4 x 1)
*          double *Cnb     IO   Cnb(3*3)
* return : none
*-----------------------------------------------------------------------------*/
void qConj(double* qnb, double* qbn)  //四元数取共轭
{

	qbn[0] = qnb[0];
	qbn[1] = -qnb[1];
	qbn[2] = -qnb[2];
	qbn[3] = -qnb[3];
}
/*----------------- -----------------------------------------------------------
* args   : 
*          double *Cnb      I    att(3*1)
*          double *qnb0    IO  qnb0 (4 x 1)
* return : none
*-----------------------------------------------------------------------------*/
void att2qnb(double* att, double* qnb) //姿态转四元数
{
	int i;
	double att_2[3];
	double sin_a[3], cos_a[3];

	for (i = 0; i < 3; i++)
	{
		att_2[i] = att[i] / 2;
		sin_a[i] = sin(att_2[i]);
		cos_a[i] = cos(att_2[i]);
	}
	qnb[0] = cos_a[0] * cos_a[1] * cos_a[2] - sin_a[0] * sin_a[1] * sin_a[2];
	qnb[1] = sin_a[0] * cos_a[1] * cos_a[2] - cos_a[0] * sin_a[1] * sin_a[2];
	qnb[2] = cos_a[0] * sin_a[1] * cos_a[2] + sin_a[0] * cos_a[1] * sin_a[2];
	qnb[3] = cos_a[0] * cos_a[1] * sin_a[2] + sin_a[0] * sin_a[1] * cos_a[2];
}

/*----------------- -----------------------------------------------------------
* args   : 
*          double *Cnb      I   qnb0(4*1)
*          double *qnb0    IO  att (3 x 1)
* return : none
*-----------------------------------------------------------------------------*/
void qnb2att(double* qnb, double* att) //四元数转姿态
{
	double Cnb11 = 0, Cnb12 = 0, Cnb13 = 0;
	double Cnb21, Cnb22, Cnb23;
	double Cnb31, Cnb32, Cnb33;
	double q11 = qnb[0] * qnb[0], q12 = qnb[0] * qnb[1], q13 = qnb[0] * qnb[2], q14 = qnb[0] * qnb[3];
	double q22 = qnb[1] * qnb[1], q23 = qnb[1] * qnb[2], q24 = qnb[1] * qnb[3];
	double q33 = qnb[2] * qnb[2], q34 = qnb[2] * qnb[3];
	double q44 = qnb[3] * qnb[3];

	Cnb11 = q11 + q22 - q33 - q44; Cnb12 = 2 * (q23 - q14); Cnb13 = 2 * (q24 + q13);
	Cnb21 = 2 * (q23 + q14); Cnb22 = q11 - q22 + q33 - q44; Cnb23 = 2 * (q34 - q12);
	Cnb31 = 2 * (q24 - q13); Cnb32 = 2 * (q34 + q12); Cnb33 = q11 - q22 - q33 + q44;

	att[0] = asin(Cnb32);// 俯仰
	att[1] = atan2(-Cnb31, Cnb33);// 横滚
	att[2] = atan2(-Cnb12, Cnb22);// 航向角
}

/* sum matrix --------------------------------------------------------------------
*  sum  matrix by matrix (ab=a+coef*b)
*          int    n,k           I  size of matrix a,b
*          double *a,*b     I  matrix a(n x m), b (n x m)
*          double coef      I  coef
*          double *ab       IO matrix ab (n x m)
* return : none
*-----------------------------------------------------------------------------*/
void matrixSum(double* a, double* b, int n, int m, double coef, double* ab)  //矩阵求和
{
	int i, j;
	for (i = 0; i < n; i++)
	{
		for (j = 0; j < m; j++)
			if(1.0 == coef)//减少不必要浮点运算
			{
				ab[j + i * m] = a[j + i * m] + b[j + i * m];
			}
			else if(-1.0 == coef)
			{
				ab[j + i * m] = a[j + i * m] - b[j + i * m];
			}
			else
			{
				ab[j + i * m] = a[j + i * m] + coef * b[j + i * m];
			}
			
	}
}

/**********************************************************
% 3x1 vector coordinate transformation by quaternion.
% 
% Prototype: 
% Inputs: qnb - transformation quaternion
%         fb - vector to be transformed
% Output: fn - output vector, such that fn = qnb*fb*conjugation(qnb)
% 
% See also  q2mat, qconj, qmul.

************************************************************/
void qmulv(double* qnb, double* fb,double *fn) //向量fb通过四元数旋转得到矢量fn
{
	double q01 = -qnb[1] * fb[0] - qnb[2] * fb[1] - qnb[3] * fb[2];
	double q02 = qnb[0] * fb[0] + qnb[2] * fb[2] -qnb[3] * fb[1];
	double q03 = qnb[0] * fb[1] + qnb[3] * fb[0] - qnb[1] * fb[2];
	double q04 = qnb[0] * fb[2] + qnb[1] * fb[1] - qnb[2] * fb[0];
	fn[0] = -q01 * qnb[1] + q02 * qnb[0] - q03 * qnb[3] + q04 * qnb[2];
	fn[1] = -q01 * qnb[2] + q03 * qnb[0] - q04 * qnb[1] + q02 * qnb[3];
	fn[2] = -q01 * qnb[3] + q04 * qnb[0] - q02 * qnb[2] + q03 * qnb[1];
}

/**********************************************************
% Rotate a 3x1 vector by a rotation vector.
%
% Prototype: vo = rotv(rv, vi)
% Inputs: wnin - rotation vector
%         fn - input vector to be rotated
% Output: an_ - output vector result, such that an_=rv2m(wnin)*fn

************************************************************/
void rotv(double* wnin, double* fn, double* an_) //旋转向量
{
	int i;
	double n = norm(wnin, 3);
	double n2 = n * n;
	double c, f;
	double q[4];
	if (n2 < (PI / 180.0 * PI / 180.0))	// 0.017^2
	{
		double n4 = n2 * n2;
		c = 1.0 - n2 * (1.0 / F2) + n4 * (1.0 / F4);
		f = 0.5 - n2 * (1.0 / F3) + n4 * (1.0 / F5);
	}
	else
	{
		double n_2 = sqrt(n2) / 2.0;
		c = cos(n_2);
		f = sin(n_2) / n_2 * 0.5;
	}
	q[0] = c;
	for (i = 0; i < 3; i++)q[i + 1] = f * wnin[i];
	double  q01 = -q[1] * fn[0] - q[2] * fn[1] - q[3] * fn[2];
	double q02 = q[0] * fn[0] + q[2] * fn[2] - q[3] * fn[1];
	double q03 = q[0] * fn[1] + q[3] * fn[0] - q[1] * fn[2];
	double q04 = q[0] * fn[2] + q[1] * fn[1] - q[2] * fn[0];
	an_[0] = -q01 * q[1] + q02 * q[0] - q03 * q[3] + q04 * q[2];
	an_[1] = -q01 * q[2] + q03 * q[0] - q04 * q[1] + q02 * q[3];
	an_[2] = -q01 * q[3] + q04 * q[0] - q02 * q[2] + q03 * q[1];
}

void UpdateQnb(double* qnb, double* rv_ib, double* rv_in)  //四元数更新
{
	int i;
	double qnb_[4];
	double n, n2,n4,n_2;
	double rvib_[4],rvin_[4], rvib_f,rvin_f;
	double qb[4];
	n = norm(rv_ib, 3); n2 = n * n;

	if (n2 < (PI / 180.0 * PI / 180.0))	// 0.017^2
	{
		n4 = n2 * n2;
		rvib_[0]= 1.0 - n2 * (1.0 / F2) +n4 * (1.0 / F4);
		rvib_f = 0.5 - n2 * (1.0 / F3) +n4 * (1.0 / F5);
	}
	else
	{
		n_2 = sqrt(n2) / 2.0;
		rvib_[0]= cos(n_2);
		rvib_f = sin(n_2) / n_2 * 0.5;
	}
	for (i = 0; i < 3; i++) { rvib_[i+1] = rvib_f * rv_ib[i]; }
	// 四元数乘法计算
	qnbmul(qnb, rvib_,qb);
	//-----------------------------------------------------------------------
	n = norm(rv_in, 3); n2 = n * n;
	if (n2 < (PI / 180.0 * PI / 180.0))	// 0.017^2
	{
		n4 = n2 * n2;
		rvin_[0] = 1.0 - n2 * (1.0 / F2) + n4 * (1.0 / F4);
		rvin_f =  -0.5 +n2 * (1.0 / F3) -n4 * (1.0 / F5);
	}
	else
	{
		n_2 = sqrt(n2) / 2.0;
		rvin_[0] = cos(n_2);
		rvin_f = -sin(n_2) / n_2 * 0.5;
	}
	for (i = 0; i < 3; i++) { rvin_[i + 1] = rvin_f * rv_in[i]; }
	qnbmul(rvin_, qb, qnb_);
	//----单位化---------------------------------------------------------------------
	n = norm(qnb_, 4); n2 = n * n;
	if (n2 > 1.000001 || n2 < 0.999999)
	{
		for (i = 0; i < 4; i++) { qnb[i] = qnb_[i] / n; }
	}
	else
	{
		for (i = 0; i < 4; i++) { qnb[i] = qnb_[i]; }
	}
}

//当qnb2为旋转矩阵时候，qnb完成qnb1的更新
void qnbmul(double* qnb1, double* qnb2, double* qnb) //四元数乘法
{
	qnb[0] = qnb1[0] * qnb2[0] - qnb1[1] * qnb2[1] - qnb1[2] * qnb2[2] - qnb1[3] * qnb2[3];
	qnb[1] = qnb1[0] * qnb2[1] + qnb1[1] * qnb2[0] + qnb1[2] * qnb2[3] - qnb1[3] * qnb2[2];
	qnb[2] = qnb1[0] * qnb2[2] + qnb1[2] * qnb2[0] + qnb1[3] * qnb2[1] - qnb1[1] * qnb2[3];
	qnb[3] = qnb1[0] * qnb2[3] + qnb1[3] * qnb2[0] + qnb1[1] * qnb2[2] - qnb1[2] * qnb2[1];
}


double* zeros(int n, int m)  // 返回一个零n*m矩阵
{
	int i,j;
	double* p;
	if (m <= 0 || n <= 0)return 0;
	if (!(p = (double*)malloc(sizeof(double)* n * m))) 
	{
//		inav_log(INAVMD(LOG_ERR),"p fail");
		return 0;
	}

	for (i =0; i < n; i++)
	{
		for(j=0;j<m;j++)
		{
			p[i+j*n]=0.0;
		}
	}
	return p;
}

double* eyes(int n)
{
	int i;
	double* p;
	if (n <= 0)return 0;
	if (!(p=zeros(n, n)))
	{
//		inav_log(INAVMD(LOG_ERR),"p fail");
		return 0;
	}
	for (i =0; i < n; i++)
	{
		p[i + i * n] = 1.0;
	}
	return p;
}
#if 0
double* mat(int n, int m)//创建矩阵
{
	double* p;
	if (!(p = (double*)malloc(sizeof(double) * n * m))) 
	{
		inav_log(INAVMD(LOG_ERR),"p fail");
		return 0;
	}
	return p;
}
#endif

void askew(double* web, double* CW)//反对称阵
{
	CW[0] = 0.0;		CW[3] = (-web[2]);	CW[6] = web[1];
	CW[1] = web[2];		CW[4] = 0.0;		CW[7] = (-web[0]);
	CW[2] = (-web[1]);	CW[5] = web[0];		CW[8] = 0.0;
}


void symmetry(double* P_in, int n, double* P_out)  //矩阵对称化处理
{
	int i, j;
	double d = 0;
	for (i = 0; i < n; i++)
	{
		for (j = 0; j < n; j++) 
		{
			d = (P_in[i + j * n] + P_in[j + i * n])*0.5;
			P_out[i + j * n] = d;
			P_out[j + i * n] = d; 
		}
	}
}

//姿态更新四阶近似的毕卡迭代法
void rv2q(double* phi, double* qnb) //旋转矢量转四元数
{
	int i;
	double c, f;
	double n=norm(phi, 3);
	double n2 = n * n;

	if (n2 < (PI / 180.0 * PI / 180.0))	// 0.017^2
	{
		double n4 = n2 * n2;
		c = 1.0 - n2 * (1.0 / F2) + n4 * (1.0 / F4);
		f = 0.5 - n2 * (1.0 / F3) + n4 * (1.0 / F5);
	}
	else
	{
		double n_2 = n*0.5;
		c = cos(n_2);
		f = sin(n_2) / n;
	}
	qnb[0] = c; 
	for (i = 0; i < 3; i++)
	{
		qnb[i + 1] = f * phi[i];
	}
}
/******************************************************************
% Convert rotation vector to transformation matrix.
%
% Prototype: m = rv2m(rv)
% Input: rv - rotation vector
% Output: m - corresponding DCM, such that
%     m = I + sin(|rv|)/|rv|*(rvx) + [1-cos(|rv|)]/|rv|^2*(rvx)^2
%     where rvx is the askew matrix or rv.
% 
% See also  m2rv, rv2q, q2rv, a2mat, rotv.
******************************************************************/
//等效旋转矢量转变换阵
void rv2m(double* rv, double* m)
{
	//int i;
	double xx, yy,zz,n2,n;
	double a,b;
	double arvx,arvy,arvz;
	double bxx,bxy,bxz,byy,byz,bzz;
	xx=rv[0]*rv[0];yy=rv[1]*rv[1];zz=rv[2]*rv[2];
	n2=xx+yy+zz;
	if(n2<1.0e-8)
	{
		a = 1-n2*(1/6-n2/120);
		b = 0.5-n2*(1/24-n2/720);
	}
	else
	{
		n = sqrt(n2);
        a = sin(n)/n; 
		b = (1-cos(n))/n2;
	}

	arvx = a*rv[0];  arvy = a*rv[1];  arvz = a*rv[2];
	bxx = b*xx;  bxy = b*rv[0]*rv[1];  bxz = b*rv[0]*rv[2];
	byy = b*yy;  byz = b*rv[1]*rv[2];  bzz = b*zz;

	m[0]=1-byy-bzz;	m[3]=-arvz+bxy;	m[6]=arvy+bxz;
	m[1]=arvz+bxy;	m[4]=1-bxx-bzz;  m[7]=-arvx+byz;
	m[2]=-arvy+bxz; m[5]=arvx+byz;  m[8]=1-bxx-byy;
	
}


void qdelphi(double* qnb, double* phi,double *qnb_)
{
	double* temp_qnb = zeros(4, 1);
	if(NULL == temp_qnb)
	{
		return;
	}
	rv2q(phi, temp_qnb);
	qnbmul(temp_qnb, qnb,qnb_);
	free(temp_qnb);
}

double WRAP_PI(double data)
{
	double data_out = data;
	while(fabs(data_out) > PI)
	{
		if(data_out > PI)
		{
		   data_out -= (2*PI);
		}
		else if(data_out < -PI)
		{
			data_out += (2*PI);
		}
	}
	return data_out;
}
double ABS(double data)
{
	double data_out = data;
	if(data>0)
	{
		data_out = data;
    }
	else
	{
		data_out = -data;
	}
	return data_out;
}


/********************************************************************************************************
	RTKCMN
*********************************************************************************************************/
double dot(const double *a, const double *b, int n)
{
    double c=0.0;
    
    while (--n>=0) c+=a[n]*b[n];
    return c;
}
/* transform ecef to geodetic postion ------------------------------------------
* transform ecef position to geodetic position
* args   : double *r        I   ecef position {x,y,z} (m)
*          double *pos      O   geodetic position {lat,lon,h} (rad,m)
* return : none
* notes  : WGS84, ellipsoidal height
*-----------------------------------------------------------------------------*/
void ecef2pos(const double *r, double *pos)
{
    double e2=FE_WGS84*(2.0-FE_WGS84),r2=dot(r,r,2),z,zk,v=RE_WGS84,sinp;
    
    for (z=r[2],zk=0.0;fabs(z-zk)>=1E-4;) {
        zk=z;
        sinp=z/sqrt(r2+z*z);
        v=RE_WGS84/sqrt(1.0-e2*sinp*sinp);
        z=r[2]+v*e2*sinp;
    }
    pos[0]=r2>1E-12?atan(z/sqrt(r2)):(r[2]>0.0?PI/2.0:-PI/2.0);
    pos[1]=r2>1E-12?atan2(r[1],r[0]):0.0;
    pos[2]=sqrt(r2+z*z)-v;
}
/* transform geodetic to ecef position -----------------------------------------
* transform geodetic position to ecef position
* args   : double *pos      I   geodetic position {lat,lon,h} (rad,m)
*          double *r        O   ecef position {x,y,z} (m)
* return : none
* notes  : WGS84, ellipsoidal height
*-----------------------------------------------------------------------------*/
void pos2ecef(const double *pos, double *r)
{
    double sinp=sin(pos[0]),cosp=cos(pos[0]),sinl=sin(pos[1]),cosl=cos(pos[1]);
    double e2=FE_WGS84*(2.0-FE_WGS84),v=RE_WGS84/sqrt(1.0-e2*sinp*sinp);
    
    r[0]=(v+pos[2])*cosp*cosl;
    r[1]=(v+pos[2])*cosp*sinl;
    r[2]=(v*(1.0-e2)+pos[2])*sinp;
}
/* ecef to local coordinate transfromation matrix ------------------------------
* compute ecef to local coordinate transfromation matrix
* args   : double *pos      I   geodetic position {lat,lon} (rad)
*          double *E        O   ecef to local coord transformation matrix (3x3)
* return : none
* notes  : matirix stored by column-major order (fortran convention)
*-----------------------------------------------------------------------------*/
void xyz2enu(const double *pos, double *E)
{
    double sinp=sin(pos[0]),cosp=cos(pos[0]),sinl=sin(pos[1]),cosl=cos(pos[1]);
    
    E[0]=-sinl;      E[3]=cosl;       E[6]=0.0;
    E[1]=-sinp*cosl; E[4]=-sinp*sinl; E[7]=cosp;
    E[2]=cosp*cosl;  E[5]=cosp*sinl;  E[8]=sinp;
}
/* transform ecef vector to local tangental coordinate -------------------------
* transform ecef vector to local tangental coordinate
* args   : double *pos      I   geodetic position {lat,lon} (rad)
*          double *r        I   vector in ecef coordinate {x,y,z}
*          double *e        O   vector in local tangental coordinate {e,n,u}
* return : none
*-----------------------------------------------------------------------------*/
void ecef2enu(const double *pos, const double *r, double *e)
{
    double E[9];
    
    xyz2enu(pos,E);
    matmul("NN",3,1,3,1.0,E,r,0.0,e);
}
/* transform local vector to ecef coordinate -----------------------------------
* transform local tangental coordinate vector to ecef
* args   : double *pos      I   geodetic position {lat,lon} (rad)
*          double *e        I   vector in local tangental coordinate {e,n,u}
*          double *r        O   vector in ecef coordinate {x,y,z}
* return : none
*-----------------------------------------------------------------------------*/
void enu2ecef(const double *pos, const double *e, double *r)
{
    double E[9];
    
    xyz2enu(pos,E);
    matmul("TN",3,1,3,1.0,E,e,0.0,r);
}
/* transform covariance to local tangental coordinate --------------------------
* transform ecef covariance to local tangental coordinate
* args   : double *pos      I   geodetic position {lat,lon} (rad)
*          double *P        I   covariance in ecef coordinate
*          double *Q        O   covariance in local tangental coordinate
* return : none
*-----------------------------------------------------------------------------*/
void covenu(const double *pos, const double *P, double *Q)
{
    double E[9],EP[9];
    
    xyz2enu(pos,E);
    matmul("NN",3,3,3,1.0,E,P,0.0,EP);
    matmul("NT",3,3,3,1.0,EP,E,0.0,Q);
}
/* transform local enu coordinate covariance to xyz-ecef -----------------------
* transform local enu covariance to xyz-ecef coordinate
* args   : double *pos      I   geodetic position {lat,lon} (rad)
*          double *Q        I   covariance in local enu coordinate
*          double *P        O   covariance in xyz-ecef coordinate
* return : none
*-----------------------------------------------------------------------------*/
void covecef(const double *pos, const double *Q, double *P)
{
    double E[9],EQ[9];
    
    xyz2enu(pos,E);
    matmul("TN",3,3,3,1.0,E,Q,0.0,EQ);
    matmul("NN",3,3,3,1.0,EQ,E,0.0,P);
}
/*	End	***********************************************************************/