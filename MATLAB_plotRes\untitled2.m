close all;
ResDatatemp = readtable( "E:\apersonal\更改固件后采集\BDDB0B_2025-08-27_16.25.10_COM111.csv",...
'HeaderLines', 1, 'ReadVariableNames', false);
yuanjidatatemp = ResDatatemp;%readtable( "E:\apersonal\更改固件后采集\BDDB0B_2025-08-27_16.25.10_COM11.csv",...
%'HeaderLines', 1, 'ReadVariableNames', false);
figure('Name','经纬度高');
hold on; grid on;
plot(ResDatatemp{ResDatatemp{:,10}~=0,10}, ResDatatemp{ResDatatemp{:,11}~=0,11}, '.-r', 'DisplayName','标定');hold on;
plot(yuanjidatatemp{yuanjidatatemp{:,10}~=0,10}, yuanjidatatemp{yuanjidatatemp{:,11}~=0,11}, '.-b', 'DisplayName','未标定');hold on;

figure;
[m61x,m61y,m61z]=llh2enu2(ResDatatemp,10,11,12);
plot(m61x,m61y,'.r-', 'DisplayName','后处理');hold on;legend;
plot(m61x(end), m61y(end), 'ro', 'MarkerSize', 8, 'HandleVisibility', 'off');hold on;
[yjx,yjy,yjz]=llh2enu2(yuanjidatatemp,10,11,12);
plot(yjx,yjy,'.b-', 'DisplayName','GNSS');hold on;
plot(yjx(end), yjy(end), 'bo', 'MarkerSize', 8, 'HandleVisibility', 'off');

figure;
hold on; grid on;
% 修正：直接使用逻辑索引提取非零数据，并计算长度
idx = ResDatatemp{:,1} ~= 0;  % 逻辑索引，找到第一列非零的行
plot(1:sum(idx), ResDatatemp{idx,1}, '.-r', 'DisplayName','标定');  % 使用逻辑索引提取数据并绘图

idx_y = yuanjidatatemp{:,1} ~= 0;  % 对另一个数据集做同样的处理
plot(1:sum(idx_y), yuanjidatatemp{idx_y,1}, '.-b', 'DisplayName','未标定');  % 绘图
hold on;  % 保持图形，虽然 redundant，但保留以符合原代码结构

figure('Name','经纬度高');
hold on; grid on;
% 修正：直接使用逻辑索引提取非零数据，并计算长度
idx = ResDatatemp{:,1} ~= 0;  % 逻辑索引，找到第一列非零的行
plot(1:sum(idx), ResDatatemp{idx,2}, '.-r', 'DisplayName','标定');  % 使用逻辑索引提取数据并绘图

idx_y = yuanjidatatemp{:,1} ~= 0;  % 对另一个数据集做同样的处理
plot(1:sum(idx_y), yuanjidatatemp{idx_y,2}, '.-b', 'DisplayName','未标定');  % 绘图
hold on;  % 保持图形，虽然 redundant，但保留以符合原代码结构

figure('Name','经纬度高');
hold on; grid on;
% 修正：直接使用逻辑索引提取非零数据，并计算长度
idx = ResDatatemp{:,1} ~= 0;  % 逻辑索引，找到第一列非零的行
plot(1:sum(idx), ResDatatemp{idx,3}, '.-r', 'DisplayName','标定');  % 使用逻辑索引提取数据并绘图

idx_y = yuanjidatatemp{:,1} ~= 0;  % 对另一个数据集做同样的处理
plot(1:sum(idx_y), yuanjidatatemp{idx_y,3}, '.-b', 'DisplayName','未标定');  % 绘图
hold on;  % 保持图形，虽然 redundant，但保留以符合原代码结构


function [E, N, U] = llh2enu(resdata,lat_deg, lon_deg, h)
    DEG2RAD = pi/180;
    R = 6378137;  % 地球半径(米)
    lat_rad=resdata{:,lat_deg}* DEG2RAD;
    lon_rad = resdata{:,lon_deg}* DEG2RAD;
    h_rad=resdata{:,h};

    lat0_rad=resdata{1,lat_deg}* DEG2RAD;
    lon0_rad=resdata{1,lon_deg}* DEG2RAD;
    h0=resdata{1,h};

   % lat_rad = lat_deg(:) * DEG2RAD;  % 转为列向量+弧度
   % lon_rad = lon_deg(:) * DEG2RAD;
    dLat = lat_rad - lat0_rad;
    dLon = lon_rad - lon0_rad;
    E = (R + h_rad(:)) .* cos(lat0_rad) .* dLon;
    N = (R + h_rad(:)) .* dLat;
    U = h_rad(:) - h0;
end
function [E, N, U] = llh2enu2(resdata, lat_deg_col, lon_deg_col, h_col)
    DEG2RAD = pi / 180;
    R = 6378137;  % 地球半径(米)
    
    % 提取数据列
    lat_deg = resdata{:, lat_deg_col};
    lon_deg = resdata{:, lon_deg_col};
    h_rad = resdata{:, h_col};  % 高度数据，用于判断是否为有效数据（非0）
    
    % 转换为弧度
    lat_rad = lat_deg * DEG2RAD;
    lon_rad = lon_deg * DEG2RAD;
    
    % 基准点（第一个有效数据点，跳过开头的0元素）
    valid_idx = find(h_rad ~= 0, 1, 'first');  % 找到第一个非0的索引
    if isempty(valid_idx)
        error('没有有效的非0高度数据！');
    end
    lat0_rad = lat_rad(valid_idx);
    lon0_rad = lon_rad(valid_idx);
    h0 = h_rad(valid_idx);
    
    % 去除开头的0元素数据（从valid_idx开始）
    lat_rad = lat_rad(valid_idx:end);
    lon_rad = lon_rad(valid_idx:end);
    h_rad = h_rad(valid_idx:end);
    
    % 计算差值
    dLat = lat_rad - lat0_rad;
    dLon = lon_rad - lon0_rad;
    
    % 计算ENU坐标
    E = (R + h_rad) .* cos(lat0_rad) .* dLon;
    N = (R + h_rad) .* dLat;
    U = h_rad - h0;
end