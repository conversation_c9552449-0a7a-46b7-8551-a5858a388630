close all;
acc_data = readtable("E:\apersonal\组合算法后处理VS\data_collection\truck\daoyuan\更换安装位置.csv");
fs = 200;  % 采样频率
yuanji=readtable("E:\apersonal\组合算法后处理VS\组合算法后处理VS\POST_Raw_Version_LiGong\data\对比文件\原极数据\SN_2025_0731_1536_52_RESULT.csv");
fsyuanji=100;
 
figure;subplot(3,1,1); plot(1:length(acc_data{:,7}), acc_data{:,7}); 
subplot(3,1,2); plot(1:length(acc_data{:,8}), acc_data{:,8}); 
subplot(3,1,3); plot(1:length(acc_data{:,9}), acc_data{:,9}); 

figure;subplot(3,1,1); plot(1:length(acc_data{:,11}), acc_data{:,11}); 
subplot(3,1,2); plot(1:length(acc_data{:,12}), acc_data{:,12}); 
subplot(3,1,3); plot(1:length(acc_data{:,13}), acc_data{:,13});
% 循环分析 11~16 列
for col = 7:9
    analyze_acceleration_spectrum(acc_data, col, fs);
end
for col = 11:13
    analyze_acceleration_spectrum(acc_data, col, fs);
end
figure;subplot(3,1,1); plot(1:length(yuanji{:,15}), yuanji{:,15}); 
subplot(3,1,2); plot(1:length(yuanji{:,16}), yuanji{:,16}); 
subplot(3,1,3); plot(1:length(yuanji{:,17}), yuanji{:,17}); 

figure;subplot(3,1,1); plot(1:length(yuanji{:,18}), yuanji{:,18}); 
subplot(3,1,2); plot(1:length(yuanji{:,19}), yuanji{:,19}); 
subplot(3,1,3); plot(1:length(yuanji{:,20}), yuanji{:,20});


for col = 15:20
    analyze_acceleration_spectrum(yuanji, col, fsyuanji);
end


function analyze_acceleration_spectrum(acc_data, col_index, fs)
    % 输入参数:
    %   acc_data: 从 readtable 读取的表格数据
    %   col_index: 要分析的列索引（如 11, 12,...,16）
    %   fs: 采样频率 (Hz)

    % 检查列是否存在
    if col_index > width(acc_data)
        error('列索引超出数据范围！');
    end

    % 提取当前列数据
    data = acc_data{:, col_index};
    
    % 检查数据是否为数值类型
    if ~isnumeric(data)
        error('数据列必须为数值类型！');
    end

    % 数据长度和时间轴
    N = length(data);
    t = (0:N-1)/fs;

    % 1. 去除均值（消除直流分量）
    data_detrend = data; %- mean(data);

    % 2. 计算FFT
    fft_data = fft(data_detrend);
    P2 = abs(fft_data/N);         % 双边谱
    P1 = P2(1:floor(N/2)+1);      % 单边谱（兼容奇偶长度）
    P1(2:end-1) = 2*P1(2:end-1);  % 修正幅值

    % 3. 频率轴
    f = fs*(0:(length(P1)-1))/N;

    % 4. 绘制频谱图
    figure('Name', sprintf('列 %d 加速度频谱分析', col_index));
    plot(f, P1, 'LineWidth', 1.5);
    xlabel('频率 (Hz)');
    ylabel('幅值');
    title(sprintf('加速度数据频谱分析 (列 %d)', col_index));
    grid on;

    % 标注关键频率
    hold on;
    xline(5, '--r', '低频噪声上限 (5Hz)');
    xline(50, '--g', '高频噪声下限 (50Hz)');
    legend('频谱', 'Location', 'best');
    hold off;
end