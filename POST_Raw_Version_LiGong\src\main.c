/***********************************************************************************
MAIN function of INAV numerical simulation 
All rights reserved I-NAV 2023 2033
***********************************************************************************/
/***********************************************************************************
Modification history

|-----------+---------------+-------------------|
|Author          |    Date               |    Done                      |
|-----------+---------------+-------------------|
|DengWei       |  2023-2-4          | First Creation             |
|DengWei       |  2023-2-13          | First Creation            |
Add print out configdata 
|-----------+---------------+-------------------|  
***********************************************************************************/
/*****************************************************************
代码风格:
1,结构体均使用大写字母+下划线组成
2. 结构体变量均使用小写字母
3. 全局变量均以g_XXX开始
4. 函数使用首字母大写方式
******************************************************************/
/**************************************************************************************
***************************************************************************************/
#define _CRT_SECURE_NO_WARNINGS
#include "main.h"
#include "stdlib.h"
#include <time.h>
//#include <unistd.h>
//#include <termios.h>

#include "nav_includes.h"


#define 	INAVSIN_VERSION			"1.0.0"

//#define OBS_FILENAME 			"../data/622-3b-1.csv"//导入观测量数据
//#define RESULT_FILENAME		"../data/inavresult-1.txt"//输出位置、姿态信息数据
//#define DATA_FILENAME			"../data/inavdata-1.csv"//包含IMU、GPS、ODS原始数据
//#define STANDARD_FILENAME		"../data/inavstandard-1.csv"//协方差矩阵数据
//#define OBS_FILENAME 			"../data/622-3b-2.csv"//导入观测量数据
//#define RESULT_FILENAME		"../data/inavresult-2.txt"//输出位置、姿态信息数据
//#define DATA_FILENAME			"../data/inavdata-2.csv"//包含IMU、GPS、ODS原始数据
//#define STANDARD_FILENAME		"../data/inavstandard-2.csv"//协方差矩阵数据

//#define OBS_FILENAME 			"C://Users//huyang//Desktop//POST_INS622_3B//622-3b-3.csv"//导入观测量数据sec31
//#define OBS_FILENAME 			"../data/622-3b-3.csv"//导入观测
//#define RESULT_FILENAME		    "../data/inavresult-3.txt"//输出位置、姿态信息数据
//#define DATA_FILENAME			"../data/inavdata-3.csv"//包含IMU、GPS、ODS原始数据
//#define STANDARD_FILENAME		"../data/inavstandard-3.csv"//协方差矩阵数据

//#define OBS_FILENAME 			"../data/inavobs3.csv"//导入观测量数据
//#define RESULT_FILENAME		"../data/inavresult3.txt"//输出位置、姿态信息数据
//#define DATA_FILENAME			"../data/inavdata3.csv"//包含IMU、GPS、ODS原始数据
//#define STANDARD_FILENAME		"../data/inavstandard3.csv"//协方差矩阵数据
//#define OBS_FILENAME 			"../data/632精准.csv"//导入观测量数据
//#define RESULT_FILENAME		"../data/inavresult精准.txt"//输出位置、姿态信息数据
//#define DATA_FILENAME			"../data/inavdata精准.csv"//包含IMU、GPS、ODS原始数据
//#define STANDARD_FILENAME		"../data/inavstandard精准.csv"//协方差矩阵数据

//********导入观测数据********->***********
//#define OBS_FILENAME 			"C://Users//huyang//Desktop//POST_INS622_3B//POST_raw_version//tlh_ins632332_20231121//data//622-3b-3.csv"//导入观测量数据sec31
//#define OBS_FILENAME 			"../data/622-3b-3.csv"//导入观测    _2024-08-08_14.09.23_F0
//#define OBS_FILENAME 			"../data/HAVE_RTK.csv"


//*****20240912采集的两组数据****************************
//#define OBS_FILENAME 			"../data/RTK_20240912.csv"
//#define OBS_FILENAME 			"../data/NORTK_20240912.csv"
//*****20240918采集的两组数据******************************
#define OBS_FILENAME 			"E:/apersonal/更改固件后采集/实测失锁/old3.csv"

// #define OBS_FILENAME 			"../data/debug_enable_170_origin_data_parsed.csv"

#define RESULT_FILENAME		    "../data/inavresult-3.txt"//输出组合算法的位置、姿态信息数据
#define DATA_FILENAME			"../data/inavdata-3.csv"//包含IMU、GPS、ODS原始数据
#define STANDARD_FILENAME		"../data/inavstandard-3.csv"//协方差矩阵数据
//****************************<-******
#define repeat 1

#define strtok_r strtok_s
#define BUFF_LEN				4096
char g_DY_NAME[128]={0};
extern double Diff_Gn_ACC;

long g_timetamp=0;
extern double Zk_m[3];
extern double Ztest[3];
static int g_uartFd = -1;
//static int speed_arr[]=
//{
//	B115200,B57600,B38400,B19200,B9600,B4800,B2400,B1200
//};

static int name_arr[]=
{
	115200,57600,38400,19200,9600,4800,2400,1200
};

typedef enum  
{
    I_NAV_SIN_SAVE_OBS =0,       				//保存观测量数据
    I_NAV_SIN_STATIC_TEST=1,				//静态算法验证模式
    I_NAV_SIN_AFTERWARDS=2,				//读取观测量数据，事后处理模式
    I_NAV_SIN_REALTIME=3,	     			//实时处理模式，需要添加代码从串口获取数据
    I_NAV_SIN_DY_AFTERWARDS=4,					//导远数据事后处理
}EINAVSINWORKMODE;
/*********************************************/
void extract_filename_without_extension(const char* filepath,
	char* output1, char* output2,
	char* output3, char* output4,
	char* output5) {
	// 1. ?????????????????????
	char base_name[256] = { 0 };

	// ?????????б???б???·?????????
	const char* last_slash = strrchr(filepath, '/');
	if (!last_slash) {
		last_slash = strrchr(filepath, '\\');
	}

	// ?????????????????·????
	const char* filename_start = last_slash ? last_slash + 1 : filepath;

	// ?????????????????????
	const char* last_dot = strrchr(filename_start, '.');

	// ?????????????????????????
	size_t length = last_dot ? (size_t)(last_dot - filename_start) : strlen(filename_start);

	// ?????????????????????????
	strncpy(base_name, filename_start, length);
	base_name[length] = '\0';

	// 2. ?????·???????????????
	char dir_path[256] = { 0 };
	if (last_slash) {
		strncpy(dir_path, filepath, last_slash - filepath + 1); // ?????β?? '/' ?? '\'
		dir_path[last_slash - filepath + 1] = '\0';
	}
	else {
		dir_path[0] = '\0'; // ??·???????????
	}
	int sdfjl = repeat;
	// 3. ???????????????????????·????
	if (sdfjl==1) {
		snprintf(output1, 256, "%s%s_inadata.csv", dir_path, base_name);
		snprintf(output2, 256, "%s%s_result.txt", dir_path, base_name);
		snprintf(output3, 256, "%s%s_standard.csv", dir_path, base_name);
		snprintf(output4, 256, "%s%s_prior.txt", dir_path, base_name);
		snprintf(output5, 256, "%s%s_feedback.txt", dir_path, base_name);
	}
	else {
		snprintf(output1, 256, "%s%s_inadata1.csv", dir_path, base_name);
		snprintf(output2, 256, "%s%s_result1.txt", dir_path, base_name);
		snprintf(output3, 256, "%s%s_standard1.csv", dir_path, base_name);
		snprintf(output4, 256, "%s%s_prior1.txt", dir_path, base_name);
		snprintf(output5, 256, "%s%s_feedback1.txt", dir_path, base_name);

	}
}
int get_buff_from_file(char *buf, int buf_len, char* filename)
{
	FILE    *pCfgFile = NULL;
	int real_len = 0;
	if (buf == NULL)
	{
	    return RETURN_FAIL;
	}
	if ((pCfgFile = fopen(filename, "r")) == NULL)
	{
		printf("line=%d-Error :get_buff_from_file fopen failed,filename=%s\r\n",__LINE__,filename);
		return RETURN_FAIL;
	}

	real_len = fread(buf, 1, buf_len, pCfgFile);
	if (real_len <= 0)
	{
		printf("line=%d-Error :get_buff_from_file fread failed\r\n",__LINE__);
		return RETURN_FAIL;
	}
	fclose(pCfgFile);
	return RETURN_SUCESS;
}

/**************************************************************/
int read_location_config_data(CombineDataTypeDef *pCombineData)
{
	char    buf[BUFF_LEN]={0};	
	char *p = NULL;
   	char *line = NULL;
	int ret=RETURN_FAIL;
	char * bufname=NULL;

	if(RETURN_SUCESS != get_buff_from_file(buf, BUFF_LEN, "../data/sins.conf"))//****参数配置文件******
	{
		printf("Error :get_buff_from_file config.dat failed\r\n");
		return RETURN_FAIL;
	}

	line = strtok_r(buf, "\n",&bufname);
    while (line)
    {
        p = line;
        while (*p != 0)
        {
            if (*p == '=')
            {
                break;
            }
        
            p++;
        } 

        if (*p == 0)
        {
            line = strtok_r(NULL, "\n",&bufname);
            continue;
        }
		if (strstr(line, "#") != NULL)
		{
		}
		else if (strstr(line, "gnssArmLength") != NULL)
        {
        	int len_gnssArmLength=3;
        	parse_split_fnum(p+1,",",3,pCombineData->Param.gnssArmLength,&len_gnssArmLength);
        }
        else if (strstr(line, "gnssAtt_from_vehicle") != NULL)
        {
            int len_gnssAtt_from_vehicle=3;
        	parse_split_fnum(p+1,",",3,pCombineData->Param.gnssAtt_from_vehicle,&len_gnssAtt_from_vehicle);
        }
        else if (strstr(line, "OD_ArmLength") != NULL)
        {
            int len_OD_ArmLength=3;
        	parse_split_fnum(p+1,",",3,pCombineData->Param.OBArmLength,&len_OD_ArmLength);
        }
		else if (strstr(line, "OD_Att_from_vehicle") != NULL)
        {
            int len_OD_Att_from_vehicle=3;
        	parse_split_fnum(p+1,",",3,pCombineData->Param.OBAtt_from_vehicle,&len_OD_Att_from_vehicle);
        }
        else if (strstr(line, "imuSelect") != NULL)
        {
           pCombineData->imuSelect= atoi(p+1);
        }
		else if (strstr(line, "memsType") != NULL)
        {
            pCombineData->memsType= atoi(p+1);
        }
		else if (strstr(line, "wb_set") != NULL)
        {
            int len_wb_set=3;
        	parse_split_fnum(p+1,",",3,pCombineData->Param.wb_set,&len_wb_set);
        }
		else if (strstr(line, "methodType") != NULL)
        {
            pCombineData->Param.methodType= atoi(p+1);
        }
		else if (strstr(line, "simulate") != NULL)
        {
            pCombineData->Param.sim= atoi(p+1);
        }
		else if (strstr(line, "lostepoch") != NULL)
        {
            pCombineData->Param.lostepoch= atoi(p+1);
        }
		else if (strstr(line, "HP") != NULL)
        {
            pCombineData->Param.HP= atoi(p+1);
        }		
		else if (strstr(line, "Adj_Nav_Standard_flag") != NULL)
        {
            pCombineData->Adj.Nav_Standard_flag= atoi(p+1);
        }
		else if (strstr(line, "ADJ_att_ods2b_filter_deg") != NULL)
        {
           // pCombineData->Adj.att_ods2_b_filte_2= atof(p+1);
			int len_tmp = 3;
			float tmp[3] = { 0.0 };
			parse_split_fnum(p + 1, ",", 3, tmp, &len_tmp);
			pCombineData->Adj.att_ods2b_filter_deg[0] = tmp[0];
			pCombineData->Adj.att_ods2b_filter_deg[1] = tmp[1];
			pCombineData->Adj.att_ods2b_filter_deg[2] = tmp[2];
			//parse_split_fnum(p + 1, ",", 3, pCombineData->Adj.att_ods2b_filter_deg, &len_tmp);//****新增****
        }
		else if (strstr(line, "ADJ_gnssAtt_from_2vehicle") != NULL)
        {
            //pCombineData->Adj.gnssAtt_from_vehicle2[2]= atof(p+1);
			int len_tmp = 3;
			float tmp[3] = { 0.0 };
			parse_split_fnum(p + 1, ",", 3, tmp, &len_tmp);
			pCombineData->Adj.gnssAtt_from_vehicle2[0] = tmp[0];
			pCombineData->Adj.gnssAtt_from_vehicle2[1] = tmp[1];
			pCombineData->Adj.gnssAtt_from_vehicle2[2] = tmp[2];
			//parse_split_fnum(p + 1, ",", 3, pCombineData->Adj.gnssAtt_from_vehicle2, &len_tmp);//****新增****
        }
		else if (strstr(line, "Adj_gyro_off") != NULL)
        {
            int len_Adj_gyro_off=3;
			float tmp[3]={0.0};
        	parse_split_fnum(p+1,",",3,tmp,&len_Adj_gyro_off);
			pCombineData->Adj.gyro_off[0]=tmp[0];
			pCombineData->Adj.gyro_off[1]=tmp[1];
			pCombineData->Adj.gyro_off[2]=tmp[2];
        }
		else if (strstr(line, "Adj_acc_off") != NULL)
        {
            int len_Adj_acc_off=3;
			float tmp[3]={0.0};
        	parse_split_fnum(p+1,",",3,tmp,&len_Adj_acc_off);
			pCombineData->Adj.acc_off[0]=tmp[0];
			pCombineData->Adj.acc_off[1]=tmp[1];
			pCombineData->Adj.acc_off[2]=tmp[2];
        }
        else
        {
            //do nothing
        }

        line = strtok_r(NULL, "\n",&bufname);
    }
    
    return RETURN_SUCESS;
}


void ParseDYSensorData(char * buff,CombineDataTypeDef* pCombineData)
{
	//注意解析观测量与保存观测量格式一致
	char *line = NULL;
    unsigned short i=0;
	unsigned int temp_count=0;
	unsigned int temp_pps_flag=0;
	unsigned int temp_gps_use_flag=0;
	//GPS周内秒
	line=strtok(buff,",");
	//GPS是5Hz,取5Hz整数部分
	//保存当前时间戳
	g_timetamp=atoi(line)*1000;
	pCombineData->gnssInfo.gpssecond = (unsigned long)(floor(g_timetamp/200.000)*200.0);
	//*************************************GPS********************************************************
	//经度
	line=strtok(NULL,",");
	pCombineData->gnssInfo.Lon=atof(line);
	//纬度
	line=strtok(NULL,",");
	pCombineData->gnssInfo.Lat=atof(line);

	//高程
	line=strtok(NULL,",");
	pCombineData->gnssInfo.Altitude=(float)atof(line);

	//北向速度
	line=strtok(NULL,",");
	pCombineData->gnssInfo.vn=(float)atof(line);
	
	//东向速度
	line=strtok(NULL,",");
	pCombineData->gnssInfo.ve=(float)atof(line);

	//天向速度
	line=strtok(NULL,",");
	pCombineData->gnssInfo.vu=-(float)atof(line);

	//航向角
	line=strtok(NULL,",");
	pCombineData->gnssInfo.Heading=(float)atof(line);

	//俯仰角
	line=strtok(NULL,",");
	pCombineData->gnssInfo.Pitch=(float)atof(line);
	
	//横滚角
	line=strtok(NULL,",");
	pCombineData->gnssInfo.Roll=(float)atof(line);
	
	//RTK状态
	line=strtok(NULL,",");
	pCombineData->gnssInfo.rtkStatus=atoi(line);
	pCombineData->gnssInfo.PositioningState = E_GPS_POS_VALID;

	//GPS定速模式
	line=strtok(NULL,",");

	//GPS定向模式
	line=strtok(NULL,",");

	//卫星数目
	line=strtok(NULL,",");
	pCombineData->gnssInfo.StarNum=atoi(line);

	
	//*************************************IMU********************************************************
	//加速度X
	line=strtok(NULL,",");
	pCombineData->imuInfo.accelGrp[0]=atof(line);

	//加速度Y
	line=strtok(NULL,",");
	pCombineData->imuInfo.accelGrp[1]=atof(line);

	//加速度Z
	line=strtok(NULL,",");
	pCombineData->imuInfo.accelGrp[2]=atof(line);
	
    //陀螺X
    line = strtok(NULL, ",");
	pCombineData->imuInfo.gyroGrp[0]=atof(line);

	//陀螺Y
	line=strtok(NULL,",");
	pCombineData->imuInfo.gyroGrp[1]=atof(line);

	//陀螺Z
	line=strtok(NULL,",");
	pCombineData->imuInfo.gyroGrp[2]=atof(line);

	//*************************************ODS********************************************************
	//ODS计数
	line=strtok(NULL,",");
	pCombineData->canInfo.counter=atoi(line);

	//四轮速度
	line=strtok(NULL,",");
	pCombineData->canInfo.data.WheelSpeed_Front_Left=(float)atof(line);
	line=strtok(NULL,",");
	pCombineData->canInfo.data.WheelSpeed_Front_Right=(float)atof(line);
	line=strtok(NULL,",");
	pCombineData->canInfo.data.WheelSpeed_Back_Left=(float)atof(line);
	line=strtok(NULL,",");
	pCombineData->canInfo.data.WheelSpeed_Back_Right=(float)atof(line);

	//方向盘
	line=strtok(NULL,",");
	pCombineData->canInfo.data.WheelSteer=(float)atof(line);

	//里程计脉冲
	line=strtok(NULL,",");
	pCombineData->canInfo.data.OdoPulse_1=(float)atof(line);
	line=strtok(NULL,",");
	pCombineData->canInfo.data.OdoPulse_2=(float)atof(line);

	//汽车档位
	line=strtok(NULL,",");
	pCombineData->canInfo.data.Gear=atoi(line);

}


unsigned char ParseSensorData(char * buff,CombineDataTypeDef* pCombineData)
{
	//?????????????????????????
	unsigned char flag = 0;
	char *line = NULL;
    unsigned short i=0;
	unsigned int temp_count=0;
	unsigned char temp_pps_flag=0;
	unsigned char temp_gps_use_flag=0;
	//float GpsTimeIntervial=0;
	static long int epochcount=0;
	epochcount++;
	//*************************************IMU********************************************************
	//??????
	line = strtok(buff, ",");
	temp_count=atoi(line);
	
	int lock=pCombineData->gnssInfo.lockflag;
	//GPS??
	line=strtok(NULL,",");
	if (lock) {
		pCombineData->gnssInfo.gpsweek = atoi(line);
	}
	else {
		pCombineData->gnssInfo.gpsweek = 0;
	}
	//GPS??????
	line=strtok(NULL,",");
	//GPS??5Hz,?5Hz????????
	//???浱?????
	//inav_log(INAVMD(LOG_ERR),"linedata:%d",line);
	//GpsTimeIntervial = 1000.0/SAMPLE_FREQ_GNSS;
	g_timetamp=atoi(line);
	if (1) {
		pCombineData->gnssInfo.gpssecond = g_timetamp;
	}
	else {
		pCombineData->gnssInfo.gpssecond = 0;
	}
	
	//pCombineData->gnssInfo.gpssecond=floor(g_timetamp/200.000)*200.0;
	//pCombineData->gnssInfo.gpssecond=g_timetamp;
#if 0
	//???????
	if(0 !=(g_timetamp%200))
	{
		return 0; 
	}
#endif

	//pps???
	line=strtok(NULL,",");
	temp_pps_flag = atoi(line);    //*****

	//GPS??????
	line=strtok(NULL,",");
	temp_gps_use_flag=atoi(line);

	//pps??gps?????
	line=strtok(NULL,",");
	if (lock) {
		pCombineData->gnssInfo.ppsDelay = (unsigned short)atof(line);
	}
	else {
		pCombineData->gnssInfo.ppsDelay = 0;
	}

    //????X
    line = strtok(NULL, ",");
	if(E_IMU_MANU_460 == pCombineData->memsType)
	{
		pCombineData->imuInfo.gyroGrp[0]=atof(line);
	}
	else if(E_IMU_MANU_SCHA63X == pCombineData->memsType)
	{
		pCombineData->scha634Info.gyro_x=atof(line);
	}
	else if(E_IMU_MANU_ADIS16465 == pCombineData->memsType)
	{
		pCombineData->adis16465Info.gyro_x=atof(line);
	}
	else if(E_IMU_MANU_EPSON_G370 == pCombineData->memsType)
	{
		pCombineData->epsonInfo.gyro_x=atof(line);
	}

	//????Y
	line=strtok(NULL,",");
	if(E_IMU_MANU_460 == pCombineData->memsType)
	{
		pCombineData->imuInfo.gyroGrp[1]=atof(line);
	}
	else if(E_IMU_MANU_SCHA63X == pCombineData->memsType)
	{
		pCombineData->scha634Info.gyro_y=atof(line);
	}
	else if(E_IMU_MANU_ADIS16465 == pCombineData->memsType)
	{
		pCombineData->adis16465Info.gyro_y=atof(line);
	}
	else if(E_IMU_MANU_EPSON_G370 == pCombineData->memsType)
	{
		pCombineData->epsonInfo.gyro_y=atof(line);
	}

	//????Z//??????????????
	line=strtok(NULL,",");
	if(E_IMU_ATT_IFOG == pCombineData->imuSelect)
	{
		pCombineData->ifogInfo.gyroGrp[2]=atof(line);
	}
	else if(E_IMU_MANU_460 == pCombineData->memsType)
	{
		pCombineData->imuInfo.gyroGrp[2]=atof(line);
	}
	else if(E_IMU_MANU_SCHA63X == pCombineData->memsType)
	{
		pCombineData->scha634Info.gyro_z=atof(line);
	}
	else if(E_IMU_MANU_ADIS16465 == pCombineData->memsType)
	{
		pCombineData->adis16465Info.gyro_z=atof(line);
	}
	else if(E_IMU_MANU_EPSON_G370 == pCombineData->memsType)
	{
		pCombineData->epsonInfo.gyro_z=atof(line);
	}
#if 0	
	if(E_IMU_ATT_IFOG == pCombineData->imuSelect)
	{
        pCombineData->ifogInfo.gyroGrp[2]=atof(line);
	}
	else
	{
		pCombineData->imuInfo.gyroGrp[2]=atof(line);
	}
#endif	
		

	//???
	line=strtok(NULL,",");
	if(E_IMU_MANU_460 == pCombineData->memsType)
	{
		pCombineData->imuInfo.sensorTemp=atof(line);
	}
	else if(E_IMU_MANU_SCHA63X == pCombineData->memsType)
	{
		pCombineData->scha634Info.temp_due=atof(line);//*****new write***
	}
	else if(E_IMU_MANU_ADIS16465 == pCombineData->memsType)
	{
		pCombineData->adis16465Info.temp=atof(line);
	}
	else if(E_IMU_MANU_EPSON_G370 == pCombineData->memsType)
	{
		pCombineData->epsonInfo.temp=atof(line);
	}

	//?????X
	line=strtok(NULL,",");
	if(E_IMU_MANU_460 == pCombineData->memsType)
	{
		pCombineData->imuInfo.accelGrp[0]=atof(line);
	}
	else if(E_IMU_MANU_SCHA63X == pCombineData->memsType)
	{
		pCombineData->scha634Info.acc_x=atof(line);
	}
	else if(E_IMU_MANU_ADIS16465 == pCombineData->memsType)
	{
		pCombineData->adis16465Info.acc_x=atof(line);
	}
	else if(E_IMU_MANU_EPSON_G370 == pCombineData->memsType)
	{
		pCombineData->epsonInfo.acc_x=atof(line);
	}

	//?????Y
	line=strtok(NULL,",");
	if(E_IMU_MANU_460 == pCombineData->memsType)
	{
		pCombineData->imuInfo.accelGrp[1]=atof(line);
	}
	else if(E_IMU_MANU_SCHA63X == pCombineData->memsType)
	{
		pCombineData->scha634Info.acc_y=atof(line);
	}
	else if(E_IMU_MANU_ADIS16465 == pCombineData->memsType)
	{
		pCombineData->adis16465Info.acc_y=atof(line);
	}
	else if(E_IMU_MANU_EPSON_G370 == pCombineData->memsType)
	{
		pCombineData->epsonInfo.acc_y=atof(line);
	}

	//?????Z
	line=strtok(NULL,",");
	if(E_IMU_MANU_460 == pCombineData->memsType)
	{
		pCombineData->imuInfo.accelGrp[2]=atof(line);
	}
	else if(E_IMU_MANU_SCHA63X == pCombineData->memsType)
	{
		pCombineData->scha634Info.acc_z=atof(line);
	}
	else if(E_IMU_MANU_ADIS16465 == pCombineData->memsType)
	{
		pCombineData->adis16465Info.acc_z=atof(line);
	}
	else if(E_IMU_MANU_EPSON_G370 == pCombineData->memsType)
	{
		pCombineData->epsonInfo.acc_z=atof(line);
	}


	//*************************************GPS********************************************************
	//????
	line=strtok(NULL,",");
	if(lock){
		pCombineData->gnssInfo.Lon=atof(line);
	}
	else{
		pCombineData->gnssInfo.Lon=0;
	}
	//???
	line=strtok(NULL,",");
	if(lock){
		pCombineData->gnssInfo.Lat=atof(line);
	}
	else{
		pCombineData->gnssInfo.Lat=0;
	}
	//???
	line=strtok(NULL,",");
	if(lock){
		pCombineData->gnssInfo.Altitude=(float)atof(line);
	}
	else{
		pCombineData->gnssInfo.Altitude=0;
	}

	//???????
	line=strtok(NULL,",");
	if(lock){
		pCombineData->gnssInfo.ve=(float)atof(line);
	}
	else{
		pCombineData->gnssInfo.ve=0;
	}
	

	//???????
	line=strtok(NULL,",");
	if(lock){
		pCombineData->gnssInfo.vn=(float)atof(line);
	}
	else{
		pCombineData->gnssInfo.vn=0;
	}

	//???????
	line=strtok(NULL,",");
	if(lock){
		pCombineData->gnssInfo.vu=(float)atof(line);
	}
	else{
		pCombineData->gnssInfo.vu=0;
	}

	//??????
	line=strtok(NULL,",");
	if(lock){
		pCombineData->gnssInfo.Pitch=(float)atof(line);
	}
	else{
		pCombineData->gnssInfo.Pitch=0;
	}

	//?????
	line=strtok(NULL,",");
	if(lock){
		pCombineData->gnssInfo.Roll=(float)atof(line);
	}
	else{
		pCombineData->gnssInfo.Roll=0;
	}

	//?????
	line=strtok(NULL,",");
	if(lock){
		pCombineData->gnssInfo.Heading=(float)atof(line);
	}
	else{
		pCombineData->gnssInfo.Heading=0;
	}

	//GPS??λ??
	line=strtok(NULL,",");
	if(lock){
		pCombineData->gnssInfo.PositioningState=atoi(line);
	}
	else{
		pCombineData->gnssInfo.PositioningState=0;
	}
	//pCombineData->gnssInfo.PositioningState='A';

	//RTK??
	line=strtok(NULL,",");
	if(lock){
		pCombineData->gnssInfo.rtkStatus=atoi(line);
	}
	else{
		pCombineData->gnssInfo.rtkStatus=0;
	}

	//???????
	line=strtok(NULL,",");
	if(lock){
		pCombineData->gnssInfo.StarNum=atoi(line);
	}
	else{
		pCombineData->gnssInfo.StarNum = 0;
	}
	
	//*************************************ODS********************************************************
	//ODS????
	line=strtok(NULL,",");
	pCombineData->canInfo.counter=atoi(line);

	//???????
	line=strtok(NULL,",");
	pCombineData->canInfo.data.WheelSpeed_Front_Left=(float)atof(line);
	line=strtok(NULL,",");
	pCombineData->canInfo.data.WheelSpeed_Front_Right=(float)atof(line);
	line=strtok(NULL,",");
	pCombineData->canInfo.data.WheelSpeed_Back_Left=(float)atof(line);
	line=strtok(NULL,",");
	pCombineData->canInfo.data.WheelSpeed_Back_Right=(float)atof(line);

	//??????
	line=strtok(NULL,",");
	pCombineData->canInfo.data.WheelSteer=(float)atof(line);

	//????????
	line=strtok(NULL,",");
	pCombineData->canInfo.data.OdoPulse_1=(float)atof(line);
	line=strtok(NULL,",");
	pCombineData->canInfo.data.OdoPulse_2=(float)atof(line);

	//??????λ
	line=strtok(NULL,",");
	pCombineData->canInfo.data.Gear=atoi(line);


	//?????????
	line=strtok(NULL,",");//Adj.Nav_Standard_flag
	line=strtok(NULL,",");//Adj.gnssAtt_from_vehicle2[0]
	line=strtok(NULL,",");//Adj.gnssAtt_from_vehicle2[1]
	line=strtok(NULL,",");//Adj.gnssAtt_from_vehicle2[2]
	line=strtok(NULL,",");//Adj.acc_off[0]
	line=strtok(NULL,",");//Adj.acc_off[1]
	line=strtok(NULL,",");//Adj.acc_off[2]
	line=strtok(NULL,",");//Adj.gyro_off[0]
	line=strtok(NULL,",");//Adj.gyro_off[1]
	line=strtok(NULL,",");//Adj.gyro_off[2]
	//pCombineData->Adj.gyro_off[0] = 0.00021872727136228087;//0.000666;
	//pCombineData->Adj.gyro_off[1] = 0.00045126103469820237;//0.0019255;
	//pCombineData->Adj.gyro_off[2] = -0.00051226074726545996;//0.000005411;
	//pCombineData->Adj.acc_off[0] = 0.087154995427837320;
	//pCombineData->Adj.acc_off[1] = -0.071200740423408265;
	//pCombineData->Adj.acc_off[2] = 0.012028342694279522;
	//combineData.Adj.gnssAtt_from_vehicle2[2]=1.67;
	///*combineData.Adj.att_ods2_b_filte_2 = ;
	//combineData.Adj.att_ods2_b_filte_2_x = ;*/
	//pCombineData->Adj.Nav_Standard_flag = 2;
	//combineData.Adj.gnssAtt_from_vehicle2[2] = 0.05;
	line=strtok(NULL,",");//trackTrue,****new write
	if (lock) {
		pCombineData->gnssInfo.trackTrue = (float)atof(line);
	}
	else {
		pCombineData->gnssInfo.trackTrue = 0;
	}
	line=strtok(NULL,",");
	line=strtok(NULL,",");
	line=strtok(NULL,",");
	line=strtok(NULL,",");
	line=strtok(NULL,",");
	line=strtok(NULL,",");
	line=strtok(NULL,",");
	if (lock) {
		pCombineData->gnssInfo.headingStatus = atoi(line);//****?????***
	}
	else {
		pCombineData->gnssInfo.headingStatus = 0;
	}
	line=strtok(NULL,",");//gpssencond982
	pCombineData->gnssInfo.gpssecond982=atoi(line);
	line = strtok(NULL, ",");
	//pCombineData->ifogInfo.gyroGrp[2] = atof(line)/ 3.013583248852681e+05;

	pCombineData->ifogInfo.gyroGrp[2] = atof(line)/ 4.04641e+05;   
	//170 fogz-70 product

	//pCombineData->ifogInfo.gyroGrp[2] = atof(line) / 1418116.324896;

#if 0
	line=strtok(NULL,",");//Nav_Status
	line=strtok(NULL,",");//Nav_Standard_flag
	line=strtok(NULL,",");//imuSelect
	line=strtok(NULL,",");//memsType
	line=strtok(NULL,",");//use_gps_flag
	//line=strtok(NULL,",");//fusion_source
	//line=strtok(NULL,",");//gps deltaT
	pCombineData->gnssInfo.deltaT = atof(line);
	line=strtok(NULL,",");//IMU deltaT
	if(E_IMU_MANU_460 == pCombineData->memsType)
	{
		pCombineData->imuInfo.deltaT=atof(line);
	}
	else if(E_IMU_MANU_SCHA63X == pCombineData->memsType)
	{
		pCombineData->scha634Info.deltaT=atof(line);
	}
	else if(E_IMU_MANU_ADIS16465 == pCombineData->memsType)
	{
		pCombineData->adis16465Info.deltaT=atof(line);
	}
	else if(E_IMU_MANU_EPSON_G370 == pCombineData->memsType)
	{
		pCombineData->epsonInfo.deltaT=atof(line);
	}

	line=strtok(NULL,",");//wheel deltaT
	pCombineData->canInfo.deltaT = atof(line);
#endif	

	//*************************************standard********************************************************
	
#if 0
	line=strtok(NULL,",");
	pNAV_Data_Full->Nav_Standard_flag = atoi(line);
	line=strtok(NULL,",");
	pNAV_Data_Full->Param.gnssAtt_from_vehicle2[0]=atof(line);
	line=strtok(NULL,",");
	pNAV_Data_Full->Param.gnssAtt_from_vehicle2[1]=atof(line);
	line=strtok(NULL,",");
	pNAV_Data_Full->Param.gnssAtt_from_vehicle2[2]=atof(line);
	line=strtok(NULL,",");
	pNAV_Data_Full->Param.acc_off[0]=atof(line);
	line=strtok(NULL,",");
	pNAV_Data_Full->Param.acc_off[1]=atof(line);
	line=strtok(NULL,",");
	pNAV_Data_Full->Param.acc_off[2]=atof(line);
	line=strtok(NULL,",");
	pNAV_Data_Full->Param.gyro_off[0]=atof(line);
	line=strtok(NULL,",");
	pNAV_Data_Full->Param.gyro_off[1]=atof(line);
	line=strtok(NULL,",");
	pNAV_Data_Full->Param.gyro_off[2]=atof(line);
#endif

	flag = 1;
	return flag;
}

int WriteStandardHead2File(FILE   *pFile)
{
	unsigned char buffer[BUFF_LEN]={0};
	int nWriteLen = 0;	
	sprintf(buffer,"gyroX_off,gyroY_off,gyroZ_off,accX_off,accY_off,accZ_off,\
		ERoll,EPitch,PHeading,Eve,Evn,Evu,ELon,ELat,EAlt,\
		Ewbx,Ewby,Ewbz,Edbx,Edby,Edbz,vehicle2_0,vehicle2_1,vehicle2_2,zk6,\
		att_ods2_b_0,att_ods2_b_1,att_ods2_b_2\n");
	nWriteLen = fwrite(buffer, 1,strlen(buffer), pFile);     
    if(0 >= nWriteLen)    
    {       
        //inav_log(INAVMD(LOG_ERR),"fwrite is error");
		inav_log(INAVMD(LOG_ERR), "fwrite is error->StandardHead2File");
        return -1;
    } 

	return 0;
}

extern double P_Cvb[3];
extern double R_m[3];
int WriteStandard2File(_NAV_Data_Full_t *pNAV_Data_Full,FILE   *pFile)
{
	unsigned char buffer[BUFF_LEN]={0};
	int nWriteLen = 0;	
	//sprintf(buffer, "%f,%f,%f12.8e,%f,%f,%f,%12.8e,%12.8e,%12.8e,%f,%f,%f,%12.8e,%12.8e,%f,%12.8e,%12.8e,%12.8e,%12.8e,%12.8e,%12.8e,%f,%f,%f,%f,%12.8f,%12.8f,%12.8f,%f,%12.8f,%f,%f,%f,%f,%f,%f,%f,%f,%f,%f,%f,%f,%f,%f,%f,%f,,%f,%f,%f,%f\n",
	sprintf(buffer, "%f,%f,%f,%f,%f,%f,%f,%f,%f,%f,%f,%f,%.20lf,%.20lf,%f,%f,%f,%f,%f,%f,%f,%f,%f,%f,%lf,%lf,%lf,%f,%f,%f,%lf,%f,%lf,%f,%f,%d,%d\n",
		pNAV_Data_Full->SINS.eb[0] * RAD2DEG,
		pNAV_Data_Full->SINS.eb[1] * RAD2DEG,
		pNAV_Data_Full->SINS.eb[2] * RAD2DEG,
		pNAV_Data_Full->SINS.db[0] / G0 * 1000,
		pNAV_Data_Full->SINS.db[1] / G0 * 1000,//MG
		pNAV_Data_Full->SINS.db[2] / G0 * 1000,
		sqrt(pNAV_Data_Full->KF.Pxk[0 + NA * 0]) * RAD2DEG,//俯仰
		sqrt(pNAV_Data_Full->KF.Pxk[1 + NA * 1]) * RAD2DEG,//横滚
		sqrt(pNAV_Data_Full->KF.Pxk[2 + NA * 2]) * RAD2DEG,//航向
		sqrt(pNAV_Data_Full->KF.Pxk[3 + NA * 3]),//ve
		sqrt(pNAV_Data_Full->KF.Pxk[4 + NA * 4]),//vn
		sqrt(pNAV_Data_Full->KF.Pxk[5 + NA * 5]),//vu
		sqrt(pNAV_Data_Full->KF.Pxk[6 + NA * 6]) * RAD2DEG,//lat
		sqrt(pNAV_Data_Full->KF.Pxk[7 + NA * 7]) * RAD2DEG,//lon
		sqrt(pNAV_Data_Full->KF.Pxk[8 + NA * 8]),//alt
		sqrt(pNAV_Data_Full->KF.Pxk[9 + NA * 9]) * RAD2DEG,//wbx
		sqrt(pNAV_Data_Full->KF.Pxk[10 + NA * 10]) * RAD2DEG,//wby
		sqrt(pNAV_Data_Full->KF.Pxk[11 + NA * 11]) * RAD2DEG,//wbz
		sqrt(pNAV_Data_Full->KF.Pxk[12 + NA * 12]) / G0 * 1000,//dbx
		sqrt(pNAV_Data_Full->KF.Pxk[13 + NA * 13]) / G0 * 1000,//dby
		sqrt(pNAV_Data_Full->KF.Pxk[14 + NA * 14]) / G0 * 1000,//dbz
		//pNAV_Data_Full->Param.gnssAtt_from_vehicle2[0],
		//pNAV_Data_Full->Param.gnssAtt_from_vehicle2[1],
		//pNAV_Data_Full->Param.gnssAtt_from_vehicle2[2],//NAV_Data_Full.Param.gnssAtt_from_vehicle2
		pNAV_Data_Full->ODS.att_ods2_b_filte[0],
		pNAV_Data_Full->ODS.att_ods2_b_filte[1],//DEG
		pNAV_Data_Full->ODS.att_ods2_b_filte[2],
		sqrt(pNAV_Data_Full->SubKF.Pk[0 + 0 * 3]) * RAD2DEG,//***PK**
		sqrt(pNAV_Data_Full->SubKF.Pk[1 + 1 * 3]) * RAD2DEG,//***PK**
		sqrt(pNAV_Data_Full->SubKF.Pk[2 + 2 * 3]) * RAD2DEG,//***PK**
		pNAV_Data_Full->SubKF.att_b2gps[0] * RAD2DEG,//deg
		pNAV_Data_Full->SubKF.att_b2gps[1] * RAD2DEG,
		pNAV_Data_Full->SubKF.att_b2gps[2] * RAD2DEG,//GNSS安装误差
		sqrt(pNAV_Data_Full->SubKF.P_b2gps) * RAD2DEG,//DEG
		pNAV_Data_Full->ODS.scale_factor_filte,
		sqrt(pNAV_Data_Full->ODS.P_wheel_fact),//
		pNAV_Data_Full->ODS.WheelSpeed_ave,//***ODS***
		pNAV_Data_Full->ODS.ODS_anglespeed * RAD2DEG,
		pNAV_Data_Full->ODS.Gear,  //***GEAR***
		pNAV_Data_Full->GPS.gpssecond


		/*
		pNAV_Data_Full->KF.Zk[6],					//航向角残差25
		pNAV_Data_Full->ODS.att_ods2_b[0],
		pNAV_Data_Full->ODS.att_ods2_b[1],
		pNAV_Data_Full->ODS.att_ods2_b[2],//28
		pNAV_Data_Full->ODS.att_ods2_b_filte[2],//29
		pNAV_Data_Full->IMU.gyro_use[2]*RAD2DEG,
		pNAV_Data_Full->SINS.vn[1],
		P_Cvb[2],
		pNAV_Data_Full->ODS.scale_factor,//33
		pNAV_Data_Full->ODS.scale_factor_filte,//34
		pNAV_Data_Full->ODS.ODS_anglespeed,//35
		pNAV_Data_Full->SINS.wb_ib[0], pNAV_Data_Full->SINS.wb_ib[1], pNAV_Data_Full->SINS.wb_ib[2],
		pNAV_Data_Full->SINS.ods2gnss,
		pNAV_Data_Full->SINS.ods2gnssfilter,	//40
		//pNAV_Data_Full->KF.Xk_clone[0], pNAV_Data_Full->KF.Xk_clone[1], pNAV_Data_Full->KF.Xk_clone[2],
		Zk_m[0], Zk_m[1], Zk_m[2],//44,45,46
		R_m[0], R_m[1], R_m[2], pNAV_Data_Full->ODS.WheelSpeed_ave
		*/
		);
	nWriteLen = fwrite(buffer, 1,strlen(buffer), pFile);     
    if(0 >= nWriteLen)    
    {       
        //inav_log(INAVMD(LOG_ERR),"fwrite is error");
		inav_log(INAVMD(LOG_ERR), "fwrite is error-> WriteStandard2File");
        return -1;
    } 

	return 0;
}

int WriteNavDataHead2File(FILE   *pFile)
{
	unsigned char buffer[BUFF_LEN]={0};
	int nWriteLen = 0;	
	sprintf(buffer,"gyroX,gyroY,gyroZ,accX,accY,accZ,Temp,Lat,Lon,Altitude,ve,vn,vu,Heading,PostionStatus,RtkStatus,SatNum,DelayPPS,GPSWeek,GPSSecond,ODSCounter,ODSSpeedFL,ODSSpeedFR,ODSSpeedBL,ODSSpeedBR,ODSSteer,ODSOdoPulse1,ODSOdoPulse2,ODSGear\n");
	nWriteLen = fwrite(buffer, 1,strlen(buffer), pFile);     
    if(0 >= nWriteLen)    
    {       
        //inav_log(INAVMD(LOG_ERR),"fwrite is error");
		inav_log(INAVMD(LOG_ERR), "fwrite is error->WriteNavDataHead2File");
        return -1;
    } 

	return 0;
}

int WriteNavData2File(_NAV_Data_Full_t *pNAV_Data_Full,FILE   *pFile)
{
	unsigned char buffer[BUFF_LEN]={0};
	int nWriteLen = 0;	
	sprintf(buffer,"%f,%f,%f,%f,%f,%f,%f,%.8f,%.8f,%f,%f,%f,%f,%f,%d,%d,%d,%f,%d,%d,%d,%d,%f,%f,%d,%f,%f,%f,%f,%f,%f,%f,%d,%d,%d,%d,%d,%d,%f\n",
		pNAV_Data_Full->IMU.gyro_use[0],
		pNAV_Data_Full->IMU.gyro_use[1],
		pNAV_Data_Full->IMU.gyro_use[2],
		pNAV_Data_Full->IMU.acc[0],
		pNAV_Data_Full->IMU.acc[1],
		pNAV_Data_Full->IMU.acc[2],
		pNAV_Data_Full->IMU.temp_use,
		pNAV_Data_Full->GPS.Lat,
		pNAV_Data_Full->GPS.Lon,
		pNAV_Data_Full->GPS.Altitude,
		pNAV_Data_Full->GPS.ve,
		pNAV_Data_Full->GPS.vn,
		pNAV_Data_Full->GPS.vu,
		pNAV_Data_Full->GPS.Heading_cor,
		pNAV_Data_Full->GPS.Position_Status,
		pNAV_Data_Full->GPS.rtkStatus,
		pNAV_Data_Full->GPS.Sate_Num,
	    pNAV_Data_Full->GPS.delay_pps,
		pNAV_Data_Full->GPS.gpsweek,
		pNAV_Data_Full->GPS.gpssecond,
		pNAV_Data_Full->GPS.gpssecond982,
		pNAV_Data_Full->GPS.headingStatus,
		pNAV_Data_Full->GPS.pdop,
		pNAV_Data_Full->GPS.trackTrue,
		pNAV_Data_Full->ODS.counter,
		pNAV_Data_Full->ODS.WheelSpeed_Front_Left,
		pNAV_Data_Full->ODS.WheelSpeed_Front_Right,
		pNAV_Data_Full->ODS.WheelSpeed_Back_Left,
		pNAV_Data_Full->ODS.WheelSpeed_Back_Right,
		pNAV_Data_Full->ODS.WheelSteer,
		pNAV_Data_Full->ODS.OdoPulse_1,
		pNAV_Data_Full->ODS.OdoPulse_2,
		pNAV_Data_Full->ODS.Gear,
		pNAV_Data_Full->GPS.gps_up_flag,
		pNAV_Data_Full->GPS.NO_RTK_heading_flag,
		pNAV_Data_Full->ODS.ods_caculat_flag,
		pNAV_Data_Full->Nav_Status,
		pNAV_Data_Full->Nav_Standard_flag,
		fabs(pNAV_Data_Full->EARTH.gn[2]));//0:标定未完成  1：标定完成
	
	nWriteLen = fwrite(buffer, 1,strlen(buffer), pFile);     
    if(0 >= nWriteLen)    
    {       
        //inav_log(INAVMD(LOG_ERR),"fwrite is error");
		inav_log(INAVMD(LOG_ERR), "fwrite is error->WriteNavData2File");
        return -1;
    } 

	return 0;
}

void WriteDataOut2File(_NAV_Data_Out_t *pNAV_Data_Out,FILE   *pFile)
{
	unsigned char buffer[BUFF_LEN]={0};
	int nWriteLen = 0;	
	if(0==pNAV_Data_Out->latitude || 0== pNAV_Data_Out->longitude)
	{
		return;
	}
	memset(buffer,0,sizeof(buffer));
	sprintf(buffer, "%d,%.8f,%.8f,%f,%f,%f,%f,%f,%f,%f,%d,%f,%d,%f,%f\n",
		g_timetamp,
		pNAV_Data_Out->latitude,
		pNAV_Data_Out->longitude,
		pNAV_Data_Out->altitude,
		pNAV_Data_Out->ve,
		pNAV_Data_Out->vn,
		pNAV_Data_Out->vu,
		pNAV_Data_Out->pitch,
		pNAV_Data_Out->roll,
		pNAV_Data_Out->heading,
		pNAV_Data_Out->Nav_Status,
		pNAV_Data_Out->heading_gps,
		pNAV_Data_Out->rtkStatus,
		pNAV_Data_Out->delay,
		pNAV_Data_Out->pdop);

		//Diff_Gn_ACC);

	nWriteLen = fwrite(buffer, 1,strlen(buffer), pFile);     
    if(0 >= nWriteLen)    
    {       
        //inav_log(INAVMD(LOG_ERR),"fwrite is error");
		inav_log(INAVMD(LOG_ERR), "fwrite is error->WriteDataOut2File");
        return;
    } 
	
}
void WriteDataOut2selfFile( _NAV_Data_Out_t* pNAV_Data_Out, _NAV_Data_Full_t* pNAV_Data_Full, FILE* pFile) {


	unsigned char buffer[BUFF_LEN] = { 0 };
	int nWriteLen = 0;
	//if (0 == pNAV_Data_Out->latitude || 0 == pNAV_Data_Out->longitude)
	//{
		//return;
	//}
//	if (NAV_Data_Out.epoch == 0) {

	//}
	double att[3];
	double pos[3];
	double gpsv[3];
	if (E_GPS_IS_UPDATE == pNAV_Data_Full->GPS.gps_up_flag) {
		att[0] = pNAV_Data_Full->GPS.Pitch;
		att[1] = pNAV_Data_Full->GPS.Roll;
		att[2] = pNAV_Data_Full->GPS.Heading_cor;
		pos[0] = pNAV_Data_Full->GPS.Lat;
		pos[1] = pNAV_Data_Full->GPS.Lon;
		pos[2] = pNAV_Data_Full->GPS.Altitude;
		gpsv[0] = pNAV_Data_Full->GPS.ve;
		gpsv[1] = pNAV_Data_Full->GPS.vn;
		gpsv[2] = pNAV_Data_Full->GPS.vu;
	}
	else {
		att[0] = 0;
		att[1] = 0;
		att[2] = 0;
		pos[0] = 0;
		pos[1] = 0;
		pos[2] = 0;
		 gpsv[0] =0;
		 gpsv[1] =0;
		 gpsv[2] =0;
	}
	memset(buffer, 0, sizeof(buffer));
	sprintf(buffer, "%.8f,%.8f,%.8f,%.8f,%.8f,%.8f,%.8f,%.8f,%.8f,%.8f,%.8f,%.8f,%.8f,%.8f,%.8f,%.8f,%.8f,%.8f,%.8f,%.8f,%.8f,%.8f,%.8f,%.8f,%.8f,%.8f,%.8f,%.8f,%.8f,%.8f,%.8f,%.8f,%.8f,%.8f,%.8f,%.8f,%.8f,%.8f,%.8f,%.8f,%.8f,%.8f,%.8f,%.8f,%.8f\n",
		NAV_Data_Out.differ_pri_b[0],//1
		NAV_Data_Out.differ_pri_b[1],//2
		NAV_Data_Out.differ_pri_b[2],//3
		NAV_Data_Out.differ_pri_b[3],//?????????в????λ?? 4
		NAV_Data_Out.differ_pri_b[4],//5
		NAV_Data_Out.differ_pri_b[5],//6
		NAV_Data_Out.differ_pri[0],//7
		NAV_Data_Out.differ_pri[1],//8
		NAV_Data_Out.differ_pri[2],//9
		NAV_Data_Out.differ_pri[3],//gps???????????ins-gps(δ??????=gps??? 10
		NAV_Data_Out.differ_pri[4],//11
		NAV_Data_Out.differ_pri[5],//12
		NAV_Data_Out.attins_pri[0],//?????ins???? 13
		NAV_Data_Out.attins_pri[1],////?????gps???? 14
		NAV_Data_Out.attins_pri[2],//?????????? 15
		NAV_Data_Out.gpspos_pre[0], //gpsλ???δ??????????5hz?????gpsλ?? 16
		NAV_Data_Out.gpspos_pre[1], //17
		NAV_Data_Out.gpspos_pre[2],//18
		NAV_Data_Out.gpspos_pre_b[0], //19
		NAV_Data_Out.gpspos_pre_b[1], ////gpsλ?????????????200hz20
		NAV_Data_Out.gpspos_pre_b[2],//21
		NAV_Data_Out.inspos_pre[0], //22
		NAV_Data_Out.inspos_pre[1],//???????insλ?? 200hz 23
		NAV_Data_Out.inspos_pre[2],//24
		NAV_Data_Out.pos_antenna[0],//25
		NAV_Data_Out.pos_antenna[1],//???????insλ?? 200hz //26
		NAV_Data_Out.pos_antenna[2],//27
		att[0],//28
		att[1],//29
		att[2],//30
		NAV_Data_Out.pitch,//31
		NAV_Data_Out.roll,//32
		NAV_Data_Out.heading,//33
		pos[0],//34
		pos[1],//35
		pos[2],//36
		gpsv[0],//37
		gpsv[1],//38
		gpsv[2],//39
		pNAV_Data_Full->SINS.vn[0],//40
		pNAV_Data_Full->SINS.vn[1],//41
		pNAV_Data_Full->SINS.vn[2],//42
		pNAV_Data_Full->SINS.pos[0]*RAD2DEG,//43
		pNAV_Data_Full->SINS.pos[1]* RAD2DEG,//44
		pNAV_Data_Full->SINS.pos[2]//45
		);

	nWriteLen = fwrite(buffer, 1, strlen(buffer), pFile);
	if (0 >= nWriteLen)
	{
		//inav_log(INAVMD(LOG_ERR),"fwrite is error");
		inav_log(INAVMD(LOG_ERR), "fwrite is error->WriteDataOut2File");
		return;
	}
}
void WriteDataOut2Feedback(_NAV_Data_Full_t* pNAV_Data_Full, FILE* pFile) {
	unsigned char buffer[BUFF_LEN] = { 0 };
	int nWriteLen = 0;
	double shuchu[15] = { 0 };
	memset(buffer, 0, sizeof(buffer));
	if (pNAV_Data_Full->shuchuflag == 1) {
		sprintf(buffer, "%.8f,%.8f,%.8f,%.8f,%.8f,%.8f,%.8f,%.8f,%.8f,%.8f,%.8f,%.8f,%.8f,%.8f,%.8f,%.8f,%.8f,%.8f,%.8f,%.8f,%.8f,%.8f,%.8f,%.8f,%.8f\n",
			pNAV_Data_Full->feedx[0],
			pNAV_Data_Full->feedx[1],
			pNAV_Data_Full->feedx[2],//3
			pNAV_Data_Full->feedv[0],
			pNAV_Data_Full->feedv[1],
			pNAV_Data_Full->feedv[2],//6
			pNAV_Data_Full->feedatt[0],
			pNAV_Data_Full->feedatt[1],
			pNAV_Data_Full->feedatt[2],//9
			pNAV_Data_Full->feedeb[0],
			pNAV_Data_Full->feedeb[1],
			pNAV_Data_Full->feedeb[2],//12
			pNAV_Data_Full->feeddb[0],
			pNAV_Data_Full->feeddb[1],
			pNAV_Data_Full->feeddb[2],//15
			pNAV_Data_Full->SINS.eb[0],//????
			pNAV_Data_Full->SINS.eb[1],//????
			pNAV_Data_Full->SINS.eb[2],//????//18
			pNAV_Data_Full->SINS.db[0],//???
			pNAV_Data_Full->SINS.db[1],//???//
			pNAV_Data_Full->SINS.db[2],//21
			RAD2DEG*pNAV_Data_Full->SubKF.att_b2gps[2],
			RAD2DEG*pNAV_Data_Full->SubKF.att_xyz[0],//
			RAD2DEG*pNAV_Data_Full->SubKF.att_xyz[1],//*********roll暂无参考意义***** //24
			RAD2DEG*pNAV_Data_Full->SubKF.att_xyz[2]

		);
	}
	else {

		sprintf(buffer, "%.8f,%.8f,%.8f,%.8f,%.8f,%.8f,%.8f,%.8f,%.8f,%.8f,%.8f,%.8f,%.8f,%.8f,%.8f,%.8f,%.8f,%.8f,%.8f,%.8f,%.8f,%.8f,%.8f,%.8f,%.8f\n",
			shuchu[0],//1
			shuchu[1],
			shuchu[2],//3
			shuchu[3],
			shuchu[4],
			shuchu[5],//6
			shuchu[6],
			shuchu[7],
			shuchu[8],//9
			shuchu[9],
			shuchu[10],
			shuchu[11],//12
			shuchu[12],
			shuchu[13],
			shuchu[14],//15
			shuchu[0],
			shuchu[1],
			shuchu[2],//18
			shuchu[3],
			shuchu[4],
			shuchu[5],//21
			shuchu[3],
			shuchu[4],
			shuchu[5],////24
			shuchu[5]////24
		);
	}

	nWriteLen = fwrite(buffer, 1, strlen(buffer), pFile);
	if (0 >= nWriteLen)
	{
		//inav_log(INAVMD(LOG_ERR),"fwrite is error");
		inav_log(INAVMD(LOG_ERR), "fwrite is error->WriteDataOut2File");
		return;
	}
}
//**********终端打印配置信息******
void PrintOutConfigData(CombineDataTypeDef *pCombineData)
{
	printf("*******************configdata*************************\n");

	printf("*****************device parameters*********************\n");
	printf("#imuSelect 0:mems 1:ifog\n");
	printf("imuSelect=%d\n",pCombineData->imuSelect);
	printf("#memsType 0: imu460/dy数据仿真 1:scha63x 2:ADI16465 3:EPSON-G370\n");
	printf("*******************************************************\n");

	printf("****************installation parameters****************\n");
	printf("gnssArmLength=%f,%f,%f\n",\
	pCombineData->Param.gnssArmLength[0],pCombineData->Param.gnssArmLength[1],pCombineData->Param.gnssArmLength[2]);
	printf("gnssAtt_from_vehicle=%f,%f,%f\n",\
	pCombineData->Param.gnssAtt_from_vehicle[0],pCombineData->Param.gnssAtt_from_vehicle[1],pCombineData->Param.gnssAtt_from_vehicle[2]);
	printf("OD_ArmLength=%f,%f,%f\n",\
	pCombineData->Param.OBArmLength[0],pCombineData->Param.OBArmLength[1],pCombineData->Param.OBArmLength[2]);
	printf("OD_Att_from_vehicle=%f,%f,%f\n",\
	pCombineData->Param.OBAtt_from_vehicle[0],pCombineData->Param.OBAtt_from_vehicle[1],pCombineData->Param.OBAtt_from_vehicle[2]);
	printf("*******************************************************\n");
	
	printf("****************Adj parameters*****************\n");
	printf("Adj Nav_Standard_flag=%d\n",pCombineData->Adj.Nav_Standard_flag);
	printf("Adj gyro_off=%f,%f,%f\n",\
	pCombineData->Adj.gyro_off[0],pCombineData->Adj.gyro_off[1],pCombineData->Adj.gyro_off[2]);
	printf("Adj acc_off=%f,%f,%f\n",\
	pCombineData->Adj.acc_off[0],pCombineData->Adj.acc_off[1],pCombineData->Adj.acc_off[2]);
	//printf("Adj gnssAtt_from_vehicle2_2=%f\n",pCombineData->Adj.gnssAtt_from_vehicle2[2]);
	//printf("Adj att_ods2_b_filte_2=%f\n",pCombineData->Adj.att_ods2_b_filte_2);
	printf("Adj_gnssAtt_from_vehicle2=%f,%f,%f\n",pCombineData->Adj.gnssAtt_from_vehicle2[0],pCombineData->Adj.gnssAtt_from_vehicle2[1],pCombineData->Adj.gnssAtt_from_vehicle2[2]);
	printf("Adj_att_ods2_b_filte=%f,%f,%f\n",pCombineData->Adj.att_ods2b_filter_deg[0], pCombineData->Adj.att_ods2b_filter_deg[1], pCombineData->Adj.att_ods2b_filter_deg[2]);
	printf("*******************************************************\n");

	printf("****************Algorithm parameters*******************\n");
	printf("#memsType set,0:kalman;1:Dead reckoning\n");
	printf("memsType=%d\n",pCombineData->memsType);
	printf("#wb_set used for dead reckoning, set imu gyro compenstation unit degree/h\n");
	printf("wb_set=%f,%f,%f\n",\
	pCombineData->Param.wb_set[0],pCombineData->Param.wb_set[1],pCombineData->Param.wb_set[2]);
	printf("#HP set,0:no;1:yes\n");
	printf("HP=%d\n",pCombineData->Param.HP);
	printf("*******************************************************\n");
	
	printf("****************Debug parameters*******************\n");
	printf("simulate=%d\n",pCombineData->Param.sim);
	printf("lostepoch=%d\n",pCombineData->Param.lostepoch);
	printf("*******************************************************\n");
}

//***************************************************

// char *paocheobs    = "../data/inavobs3.csv";//原始跑车文件
int main(int argc, char ** argv)
{
	FILE   *pObsFile = NULL; 
	FILE   *pResultFile = NULL; 
	FILE   *pNavDataFile = NULL; 
	FILE   *pStandardFile = NULL; 
	
	char  obsbuff[BUFF_LEN]={0};
	unsigned short i=0;
	unsigned long ObsNum=0;
	FILE* prifile = NULL;
	const char* filepath1 = OBS_FILENAME;
	char out1[256], out2[256], out3[256], out4[256], out5[256];

	extract_filename_without_extension(filepath1, out1, out2, out3, out4, out5);
	prifile = fopen(out4, "wb");
	if (NULL == prifile)
	{
		inav_log(INAVMD(LOG_ERR), "%s is error", out4);
		return 0;
	}
	FILE* feedfile = NULL;
	feedfile = fopen(out5, "wb");
	if (NULL == feedfile)
	{
		inav_log(INAVMD(LOG_ERR), "%s is error", out5);
		return 0;
	}
//#ifdef WIN32
	EINAVSINWORKMODE mInavWorkMode = I_NAV_SIN_AFTERWARDS;
	//EINAVSINWORKMODE mInavWorkMode = I_NAV_SIN_DY_AFTERWARDS;

//#endif
	CliShowBuildVersion();
	
	/*if (argc >= 2)
	{
		if(strcmp(argv[1], "-dy") == 0)
		{
			mInavWorkMode = I_NAV_SIN_DY_AFTERWARDS;
			//sprintf(g_DY_NAME,"%s",argv[2]);
			sprintf(g_DY_NAME,"%s",paocheobs);
			printf("DY simulate file:%s\n",g_DY_NAME);
		}
		else if(strcmp(argv[1], "?") == 0)
		{	
			printf("-dy:	using DY data  AFTERWARDS\n");
			return 0;
		}
		else if(strcmp(argv[1], "v") == 0)
		{
			printf("navapp:	Version%s\n",INAVSIN_VERSION);
			return 0;
		}
		else
		{
			printf("I_NAV_SIN_AFTERWARDS\n");
		}
		
	}
	else
	{
		printf("I_NAV_SIN_AFTERWARDS\n");
	}*/

	//sprintf(g_DY_NAME,"%s",paocheobs);

#if 0	
	_NAV_Data_Full_t test1={0};
	test1.SINS.pos[0]=90*DEG2RAD;
	test1.SINS.pos[1]=113*DEG2RAD;
	test1.SINS.pos[2]=0;
	Earth_Init(&test1);
	Earth_UP(&test1);
#endif	

	inav_set_loglevel(LOG_DEBUG);

	memset(&combineData,0,sizeof(combineData));
	memset(&NAV_Data_Full,0,sizeof(NAV_Data_Full));
	//double a=floor(354145650/200.000)*200.0;
	//初始化读取配置文件
	if(RETURN_SUCESS !=  read_location_config_data(&combineData))
	{
		printf("Error :read_localtion_config_data failed\r\n");

		return 0;
	}

	 //打印当前配置
    PrintOutConfigData(&combineData);

	/********************************车载六轴模型****************************************/
	//初始化融合采用GPS，以后需要修改到配置文件中
	combineData.fusion=E_FUNSION_GPS;
	//由于设备可能出现无法定向，rtk OK情况实际跑车需要设置，
	//仿真数据确保定向ok,保证与以前仿真数据兼容性
	//combineData.gnssInfo.headingStatus= E_GPS_RTK_FIXED;

	//应用场景配置
	NAV_Data_Full.UseCase=E_USE_CASE_In_Vehicle;
	
	//打开写导航结果文件
	pResultFile = fopen(out2, "wb");
    if (NULL == pResultFile)
    {
       inav_log(INAVMD(LOG_ERR),"%s is error",out2);	
	   return 0;
    }

	//导航数据保存
	pNavDataFile = fopen(out1, "wb");
    if (NULL == pNavDataFile)
    {
       inav_log(INAVMD(LOG_ERR),"%s is error",out1);	
	   return 0;
    }
	//WriteNavDataHead2File(pNavDataFile);

	//调试打印，标定参数
	pStandardFile = fopen(out3, "wb");
    if (NULL == pStandardFile)
    {
       inav_log(INAVMD(LOG_ERR),"%s is error",out3);	
	   return 0;
    }
	WriteStandardHead2File(pStandardFile);
	//***************初始化参数**->********
	NAV_Data_Full.ins_buffer_full_flag = RETURN_FAIL;
	NAV_Data_Full.ZUPT_flag = RETURN_FAIL;
	NAV_Data_Full.acc_gyr_Cnt = 0;
	NAV_Data_Full.Nav_Status = E_NAV_STATUS_START;
	NAV_Data_Full.EARTH.gn[2] = -G0;

	NAV_Data_Full.ins_buffer_full_cnt = 0;
	NAV_Data_Full.Modacc = 0.0;
	NAV_Data_Full.Modgyr = 0.0;
	NAV_Data_Full.Modacc_std2 = 0.0;
	NAV_Data_Full.Modgyr_std2 = 0.0;
	NAV_Data_Full.Macc[0] = 0.0;
	NAV_Data_Full.Macc[1] = 0.0;
	NAV_Data_Full.Macc[2] = 0.0;
	NAV_Data_Full.Mgyr[0] = 0.0;
	NAV_Data_Full.Mgyr[1] = 0.0;
	NAV_Data_Full.Mgyr[2] = 0.0;
	NAV_Data_Full.KF_CheckCnt = 0;
	NAV_Data_Full.Subkf2_Cnt = 0;
 NAV_Data_Full.ZUPTyaw_ST_Cnt = 0;
	NAV_Data_Full.SINS.Init_flag = RETURN_FAIL;//每次启动时默认为0，尚未初始化
	NAV_Data_Full.Pre_att_flag = RETURN_FAIL;//每次启动置0,尚未进入组合状态
	NAV_Data_Full.GPSlastnum=0;
	for (i =0;i<3;i++){
			  	{
					NAV_Data_Full.SINS.eb[i] =0;
			    	NAV_Data_Full.SINS.db[i] =0;
				}
					
	}
	//***************初始化参数**<-********
	//NAV_Data_Full.Param.gnssAtt_from_vehicle[2]=-1.70f;
	//NAV_Data_Full.ODS.att_ods2_b[2]=1.265456f;
	// ************组合算法************->************
	//事后处理模式
	{
		//inav_log(INAVMD(LOG_DEBUG), "I_NAV_SIN_AFTERWARDS");
		if(I_NAV_SIN_DY_AFTERWARDS == mInavWorkMode)
		{
			if (NULL == (pObsFile = fopen(g_DY_NAME, "rb")))    
			{		
				 //inav_log(INAVMD(LOG_ERR),"%s is error",g_DY_NAME);	
				 return 0;
			}
		}
		else
		{
			if (NULL == (pObsFile = fopen(OBS_FILENAME, "rb")))    
			{		
				 inav_log(INAVMD(LOG_ERR),"%s is error",OBS_FILENAME);	
				 return 0;
			}
		}
		ObsNum=0;
#if 0//测试命令行打印		
		CliShowHelp();
		char data[128]={0};
		strcpy(data,"$ALGO:writeConfig,Adj_acc_off,0.012,0.011,0.033\r\n");
		ParseStrCmd(data);
#endif	
		fgets(obsbuff,sizeof(obsbuff),pObsFile);//第一行不要
		combineData.gnssInfo.lockflag=1;
		while(!feof(pObsFile))
		{
			memset(obsbuff,0,sizeof(obsbuff));
			int lenn = fscanf(pObsFile,"%s",obsbuff);
			//inav_log(INAVMD(LOG_DEBUG), "obsbuff=%s",obsbuff);
			if(strlen(obsbuff) <= 5)
			{
				inav_log(INAVMD(LOG_ERR), "error obsbuff=%s",obsbuff);
				break;
			}

			if(I_NAV_SIN_DY_AFTERWARDS == mInavWorkMode)
			{
				//ParseDYSensorData(obsbuff,&combineData);
				ParseSensorData(obsbuff,&combineData);
			}
			else
			{
			
				if(1 != ParseSensorData(obsbuff,&combineData))//******异常则跳过该行数据**取代FPGA功能****
				{
					continue;
				}
			}
			
			ObsNum++;
			////仿真设置有轮速标志
			//
			wheel_is_running();//*****根据解析数据进行轮速判别*****
			//combineData.canInfo.flag = 1;
			if(E_USE_CASE_In_Vehicle == NAV_Data_Full.UseCase)
			{
				//if(ObsNum>=60000)
				// gpssecond updata 5ms,gpssecond982 update 200ms,uinit: ms
				if (//abs(combineData.gnssInfo.gpssecond - combineData.gnssInfo.gpssecond982) < 10000
					//&& combineData.gnssInfo.gpssecond982 != 0&& 
					combineData.gnssInfo.gpssecond != 0)
				NAV_function();
			}
			else if(E_USE_CASE_In_UAV == NAV_Data_Full.UseCase)
			{
				//NAV_function_UAV();
			}
			else
			{
				inav_log(INAVMD(LOG_ERR),"UseCase=%s is error",NAV_Data_Full.UseCase);
			}
			//if (NAV_Data_Full.Nav_Status > E_NAV_STATUS_SINS_KF_INITIAL)
			{
				//打印导航数据文件
				WriteNavData2File(&NAV_Data_Full, pNavDataFile);
				//打印标定数据
				WriteStandard2File(&NAV_Data_Full, pStandardFile);
				//打印输出文件
				WriteDataOut2File(&NAV_Data_Out, pResultFile);
				//打印调试信息
				WriteDataOut2selfFile(&NAV_Data_Out, &NAV_Data_Full, prifile);
				WriteDataOut2Feedback(&NAV_Data_Full, feedfile);
				NAV_Data_Full.shuchuflag = 0;
			}
		}
		
		fclose(pObsFile);
		fclose(pResultFile);
		fclose(pNavDataFile);
		fclose(pStandardFile);
		//打印ODS参数
		inav_log(INAVMD(LOG_DEBUG), "scale_factor=%f",NAV_Data_Full.ODS.scale_factor);
		inav_log(INAVMD(LOG_DEBUG), "scale_factor_filte=%f",NAV_Data_Full.ODS.scale_factor_filte);
		//inav_log(INAVMD(LOG_DEBUG), "scale_factor_filte_RTK=%f",NAV_Data_Full.ODS.scale_factor_filte_RTK);
		inav_log(INAVMD(LOG_DEBUG), "ODS.att_ods2_b_filte[0]=%f",NAV_Data_Full.ODS.att_ods2_b_filte[0]);
		inav_log(INAVMD(LOG_DEBUG), "ODS.att_ods2_b_filte[2]=%f",NAV_Data_Full.ODS.att_ods2_b_filte[2]);
		inav_log(INAVMD(LOG_DEBUG), "END I_NAV_SIN_AFTERWARDS, epoch sum:%d",ObsNum);
		
	}
	// ************组合算法************<-************
	
}
