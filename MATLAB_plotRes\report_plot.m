close all;
second=readtable("E:\apersonal\更改固件后采集\8_22\1.csv");%first=[];
first=readtable("E:\apersonal\更改固件后采集\8_22\验前观测方程8.txt");
dataStruct(1) = struct('resData', first, 'priData', second);

second2=readtable("E:\apersonal\更改固件后采集\8_22\2.csv");%first=[];
first2=readtable("E:\apersonal\更改固件后采集\8_22\验前观测方程82.txt");
dataStruct(2) = struct('resData', first2, 'priData', second2);

second3=readtable("E:\apersonal\更改固件后采集\8_22\3.csv");%first=[];
first3=readtable("E:\apersonal\更改固件后采集\8_22\验前观测方程83.txt");
dataStruct(3) = struct('resData', first3, 'priData', second3);

second4=readtable("E:\apersonal\更改固件后采集\8_22\4.csv");%first=[];
first4=readtable("E:\apersonal\更改固件后采集\8_22\验前观测方程84.txt");
dataStruct(4) = struct('resData', first4, 'priData', second4);

second5=readtable("E:\apersonal\更改固件后采集\8_22\5.csv");%first=[];
first5=readtable("E:\apersonal\更改固件后采集\8_22\验前观测方程85.txt");
dataStruct(5) = struct('resData', first5, 'priData', second5);

second6=readtable("E:\apersonal\更改固件后采集\8_22\6.csv");%first=[];
first6=readtable("E:\apersonal\更改固件后采集\8_22\验前观测方程86.txt");
dataStruct(6) = struct('resData', first6, 'priData', second6);

plotMultipleFiles(dataStruct(1),dataStruct(2),dataStruct(3),dataStruct(4),dataStruct(5),dataStruct(6))

function plotMultipleFiles(varargin)%原始输出结果分析
    flag1=1;flag2=1;%原始数据，与后处理文件
    % 输入检查：确保传入 1~6 个数据文件
    if nargin < 1
        error('至少需要传入 1 个数据文件');
    elseif nargin > 6
        error('最多只能传入 6 个数据文件');
    end
    structs = struct('resData', [], 'priData', []);
    for i = 1:nargin
            if ~isstruct(varargin{i})
                error('输入必须是结构体或结构体数组');
            end
            structs(i) = varargin{i}; 
    end
    % 确定子图布局
    numFiles =  numel(structs);
    if numFiles == 1
        layout = [1, 1]; % 单个子图
    elseif numFiles == 4
        layout = [2, 2]; % 2×2 布局
    else
        layout = [3, 2]; % 3×2 布局（适用于 2,3,5,6 个文件）
    end
    % 创建图形窗口
    figure;
    % 遍历每个数据文件并绘图
    for i = 1:numFiles
        % 读取数据（假设是数值矩阵或表格）
        currentStruct = structs(i);
        second = currentStruct.resData;
         first= currentStruct.priData;%后处理获得的定位结果
        % 创建子图
        subplot(layout(1), layout(2), i);
        if ~isempty(first)&&flag1
            plot(first{end,55}, first{end,56}, 'ro', 'MarkerSize', 8, 'HandleVisibility', 'off'); % 红色圆圈标记第55列最后一个点
            plot(first{end,14}, first{end,15}, 'bo', 'MarkerSize', 8, 'HandleVisibility', 'off'); % 蓝色圆圈标记第14列最后一个点
            plot(first{first{:,55}~=0,55}, first{first{:,55}~=0,56}, '.-r', 'DisplayName','组合导航');hold on;
            plot(first{first{:,14}~=0,14}, first{first{:,15}~=0,15}, '.-b', 'DisplayName','GNSS');hold on;legend;
        end
        if ~isempty(second)&&flag2
            plot(second{:,26}, second{:,25}, '.-k', 'DisplayName','后处理结果');hold on;
            plot(second{end,26}, second{end,25}, 'ko', 'MarkerSize', 8, 'HandleVisibility', 'off');
        end
         xlabel('X（经度）');
        ylabel('Y （纬度）');
        title('经纬度平面图');
        grid on;
    end

    % 如果子图数量不足布局容量（如 5 个文件在 3×2 布局中会空 1 个），隐藏多余子图
    if numFiles < layout(1) * layout(2)
        for j = (numFiles + 1):(layout(1) * layout(2))
            subplot(layout(1), layout(2), j);
            axis off; % 隐藏坐标轴
        end
    end

    figure;
    % 遍历每个数据文件并绘图
    for i = 1:numFiles
        % 读取数据（假设是数值矩阵或表格）
       currentStruct = structs(i);
       second = currentStruct.resData;
       first= currentStruct.priData;%原始输出文件
       subplot(layout(1), layout(2), i);
       if ~isempty(first)&&flag1
        % 创建子图
            [yjx,yjy,yjz]=llh2enu(first,14,15,16);
            [yjx1,yjy1,yjz1]=llh2enu(first,55,56,57);
            subplot(layout(1), layout(2), i);
            plot(yjx,yjy,'.b-', 'DisplayName','GNSS');hold on;
            plot(yjx1,yjy1,'.r-', 'DisplayName','组合导航');hold on;legend;
            plot(yjx(end), yjy(end), 'bo', 'MarkerSize', 8, 'HandleVisibility', 'off');
            plot(yjx1(end), yjy1(end), 'ro', 'MarkerSize', 8, 'HandleVisibility', 'off');
            xlabel('X（m）');
            ylabel('Y （m）');
            title('经纬度平面图');
            grid on;
       end
       if ~isempty(second)&&flag2
            [m61x,m61y,m61z]=llh2enu(second,26,25,27);
            plot(m61x,m61y,'.k-', 'DisplayName','后处理');hold on;legend;
            plot(m61x(end), m61y(end), 'ro', 'MarkerSize', 8, 'HandleVisibility', 'off');
       end
    end

    % 如果子图数量不足布局容量（如 5 个文件在 3×2 布局中会空 1 个），隐藏多余子图
    if numFiles < layout(1) * layout(2)
        for j = (numFiles + 1):(layout(1) * layout(2))
            subplot(layout(1), layout(2), j);
            axis off; % 隐藏坐标轴
        end
    end
     for i = 1:numFiles
        % 读取数据（假设是数值矩阵或表格）
        currentStruct = structs(i);
        second = currentStruct.resData;
        first= currentStruct.priData;%原始输出文件
        % 创建子图
        if (~isempty(first)&&flag1)||~isempty(second)&&flag2
            figure;
            subplot(3, 1, 1);
            if~isempty(first)&&flag1
                plot(1:length(first{:,61}), first{:,61}, '.r', 'DisplayName','组合导航');hold on;
                plot(1:length(first{:,20}), first{:,20}, '.b', 'DisplayName','GNSS');hold on;
            end
            if~isempty(second)&&flag2
                plot(1:length(second{:,31}),second{:,31}, '.k', 'DisplayName','后处理');hold on;
            end
            xlabel('X（历元）');ylabel('Y （°）');legend;title("解算姿态对比");legend;
            subplot(3, 1, 2);
            if~isempty(first)&&flag1
                plot(1:length(first{:,62}), first{:,62}, '.r', 'DisplayName','组合导航');hold on;
                plot(1:length(first{:,21}), first{:,21}, '.b', 'DisplayName','GNSS');hold on;
            end
            if~isempty(second)&&flag2
                plot(1:length(second{:,32}), second{:,32}, '.k', 'DisplayName','后处理');hold on;
            end
            xlabel('X（历元）');ylabel('Y （°）');legend;
            subplot(3, 1, 3);
             if~isempty(first)&&flag1
                plot(1:length(first{:,63}), first{:,63}, '.r', 'DisplayName','组合导航');hold on;
                plot(1:length(first{:,22}), first{:,22}, '.b', 'DisplayName','GNSS');hold on;
             end
             if~isempty(second)&&flag2
                plot(1:length(second{:,33}), second{:,33}, '.k', 'DisplayName','后处理');hold on;
            end
            xlabel('X（历元）');ylabel('Y （°）');legend;        
            grid on;
        end
        
     end
     for i = 1:numFiles
        % 读取数据（假设是数值矩阵或表格）
        currentStruct = structs(i);
        second = currentStruct.resData;
        first= currentStruct.priData;%原始输出文件
        % 创建子图
        if (~isempty(first)&&flag1)||~isempty(second)&&flag2
        % 创建子图
            figure;
            subplot(3, 1, 1);
            if~isempty(first)&&flag1
                plot(1:length(first{:,58}), first{:,58}, '.r', 'DisplayName','组合导航');hold on;
                plot(1:length(first{:,17}), first{:,17}, '.b', 'DisplayName','GNSS');hold on;
            end
            if~isempty(second)&&flag2
                plot(1:length(second{:,40}), second{:,40}, '.k', 'DisplayName','后处理');hold on;
            end
            xlabel('历元');ylabel('Y （m/s）');legend;
            title("解算速度对比");legend;
            subplot(3, 1, 2);
            if~isempty(first)&&flag1
                plot(1:length(first{:,59}), first{:,59}, '.r', 'DisplayName','组合导航');hold on;
                plot(1:length(first{:,18}), first{:,18}, '.b', 'DisplayName','GNSS');hold on;
            
            end
            if~isempty(second)&&flag2
                plot(1:length(second{:,41}), second{:,41}, '.k', 'DisplayName','后处理');hold on;
            end
            xlabel('历元');ylabel('Y （m/s）');legend;
            subplot(3, 1, 3);
            if~isempty(first)&&flag1
                plot(1:length(first{:,60}), first{:,60}, '.r', 'DisplayName','组合导航');hold on;
                plot(1:length(first{:,19}), first{:,19}, '.b', 'DisplayName','GNSS');hold on;
            end
            if~isempty(second)&&flag2
               plot(1:length(second{:,42}), second{:,42}, '.k', 'DisplayName','后处理');hold on; 
            end
            xlabel('历元');ylabel('Y （m/s）');legend;            
            grid on;
        end
    end
end
function [E, N, U] = llh2enu(resdata,lat_deg, lon_deg, h)
    DEG2RAD = pi/180;
    R = 6378137;  % 地球半径(米)
    lat_rad=resdata{:,lat_deg}* DEG2RAD;
    lon_rad = resdata{:,lon_deg}* DEG2RAD;
    h_rad=resdata{:,h};

    lat0_rad=resdata{1,lat_deg}* DEG2RAD;
    lon0_rad=resdata{1,lon_deg}* DEG2RAD;
    h0=resdata{1,h};

   % lat_rad = lat_deg(:) * DEG2RAD;  % 转为列向量+弧度
   % lon_rad = lon_deg(:) * DEG2RAD;
    dLat = lat_rad - lat0_rad;
    dLon = lon_rad - lon0_rad;
    E = (R + h_rad(:)) .* cos(lat0_rad) .* dLon;
    N = (R + h_rad(:)) .* dLat;
    U = h_rad(:) - h0;
end
