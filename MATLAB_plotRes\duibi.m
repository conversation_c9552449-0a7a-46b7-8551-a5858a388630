close all;
ResDatatemp = readtable('E:\apersonal\组合算法后处理VS\组合算法后处理VS\POST_Raw_Version_LiGong\data\对比文件\标定文件\验前观测方程8.txt', ...
        'HeaderLines', 1, 'ReadVariableNames', false);
ResDatatemp1 = readtable('E:\apersonal\组合算法后处理VS\组合算法后处理VS\POST_Raw_Version_LiGong\data\对比文件\标定文件\验前观测方程1.txt', ...
        'HeaderLines', 1, 'ReadVariableNames', false);
feedback= readtable('E:\apersonal\组合算法后处理VS\组合算法后处理VS\POST_Raw_Version_LiGong\data\对比文件\标定文件\反馈8.txt', ...
        'HeaderLines', 1, 'ReadVariableNames', false);
feedback1= readtable('E:\apersonal\组合算法后处理VS\组合算法后处理VS\POST_Raw_Version_LiGong\data\对比文件\标定文件\反馈1.txt', ...
        'HeaderLines', 1, 'ReadVariableNames', false);
ResDatatemp2 = readtable('E:\apersonal\组合算法后处理VS\组合算法后处理VS\POST_Raw_Version_LiGong\data\对比文件\绕圈快速\验前观测方程.txt', ...
        'HeaderLines', 1, 'ReadVariableNames', false);
ResDatatemp3 = readtable('E:\apersonal\组合算法后处理VS\组合算法后处理VS\POST_Raw_Version_LiGong\data\对比文件\绕圈快速\验前观测方程1.txt', ...
        'HeaderLines', 1, 'ReadVariableNames', false);
feedback2= readtable('E:\apersonal\组合算法后处理VS\组合算法后处理VS\POST_Raw_Version_LiGong\data\对比文件\绕圈快速\反馈.txt', ...
        'HeaderLines', 1, 'ReadVariableNames', false);
feedback3= readtable('E:\apersonal\组合算法后处理VS\组合算法后处理VS\POST_Raw_Version_LiGong\data\对比文件\绕圈快速\反馈1.txt', ...
        'HeaderLines', 1, 'ReadVariableNames', false);
ResDatatemp4 = readtable('E:\apersonal\组合算法后处理VS\组合算法后处理VS\POST_Raw_Version_LiGong\data\对比文件\朝北快速\验前观测方程.txt', ...
        'HeaderLines', 1, 'ReadVariableNames', false);
ResDatatemp5 = readtable('E:\apersonal\组合算法后处理VS\组合算法后处理VS\POST_Raw_Version_LiGong\data\对比文件\朝北快速\验前观测方程1.txt', ...
        'HeaderLines', 1, 'ReadVariableNames', false);
feedback4= readtable('E:\apersonal\组合算法后处理VS\组合算法后处理VS\POST_Raw_Version_LiGong\data\对比文件\朝北快速\反馈.txt', ...
        'HeaderLines', 1, 'ReadVariableNames', false);
feedback5= readtable('E:\apersonal\组合算法后处理VS\组合算法后处理VS\POST_Raw_Version_LiGong\data\对比文件\朝北快速\反馈1.txt', ...
        'HeaderLines', 1, 'ReadVariableNames', false);

ResDatatemp6 = readtable('E:\apersonal\组合算法后处理VS\组合算法后处理VS\POST_Raw_Version_LiGong\data\对比文件\绕圈匀速\验前观测方程.txt', ...
        'HeaderLines', 1, 'ReadVariableNames', false);
ResDatatemp7 = readtable('E:\apersonal\组合算法后处理VS\组合算法后处理VS\POST_Raw_Version_LiGong\data\对比文件\绕圈匀速\验前观测方程1.txt', ...
        'HeaderLines', 1, 'ReadVariableNames', false);
feedback6= readtable('E:\apersonal\组合算法后处理VS\组合算法后处理VS\POST_Raw_Version_LiGong\data\对比文件\绕圈匀速\反馈.txt', ...
        'HeaderLines', 1, 'ReadVariableNames', false);
feedback7= readtable('E:\apersonal\组合算法后处理VS\组合算法后处理VS\POST_Raw_Version_LiGong\data\对比文件\绕圈匀速\反馈1.txt', ...
        'HeaderLines', 1, 'ReadVariableNames', false);

ResDatatemp8 = readtable('E:\apersonal\组合算法后处理VS\组合算法后处理VS\POST_Raw_Version_LiGong\data\对比文件\朝北慢速\验前观测方程.txt', ...
        'HeaderLines', 1, 'ReadVariableNames', false);
ResDatatemp9 = readtable('E:\apersonal\组合算法后处理VS\组合算法后处理VS\POST_Raw_Version_LiGong\data\对比文件\朝北慢速\验前观测方程1.txt', ...
        'HeaderLines', 1, 'ReadVariableNames', false);
feedback8= readtable('E:\apersonal\组合算法后处理VS\组合算法后处理VS\POST_Raw_Version_LiGong\data\对比文件\朝北慢速\反馈.txt', ...
        'HeaderLines', 1, 'ReadVariableNames', false);
feedback9= readtable('E:\apersonal\组合算法后处理VS\组合算法后处理VS\POST_Raw_Version_LiGong\data\对比文件\朝北慢速\反馈1.txt', ...
        'HeaderLines', 1, 'ReadVariableNames', false);

ResDatatemp10 = readtable('E:\apersonal\组合算法后处理VS\组合算法后处理VS\POST_Raw_Version_LiGong\data\对比文件\直线快速\验前观测方程.txt', ...
        'HeaderLines', 1, 'ReadVariableNames', false);
ResDatatemp11 = readtable('E:\apersonal\组合算法后处理VS\组合算法后处理VS\POST_Raw_Version_LiGong\data\对比文件\直线快速\验前观测方程1.txt', ...
        'HeaderLines', 1, 'ReadVariableNames', false);
feedback10= readtable('E:\apersonal\组合算法后处理VS\组合算法后处理VS\POST_Raw_Version_LiGong\data\对比文件\直线快速\反馈.txt', ...
        'HeaderLines', 1, 'ReadVariableNames', false);
feedback11= readtable('E:\apersonal\组合算法后处理VS\组合算法后处理VS\POST_Raw_Version_LiGong\data\对比文件\直线快速\反馈1.txt', ...
        'HeaderLines', 1, 'ReadVariableNames', false);



yuanjidatatemp = readtable( 'E:\apersonal\组合算法后处理VS\组合算法后处理VS\POST_Raw_Version_LiGong\data\对比文件\原极数据\SN_2025_0731_1536_52_RESULT.csv',...
'HeaderLines', 1, 'ReadVariableNames', false);
yuanjidatatemp1 = readtable( 'E:\apersonal\组合算法后处理VS\组合算法后处理VS\POST_Raw_Version_LiGong\data\对比文件\原极数据\换位置-第一次绕圈，快速_RESULT.csv',...
'HeaderLines', 1, 'ReadVariableNames', false);
yuanjidatatemp2 = readtable( 'E:\apersonal\组合算法后处理VS\组合算法后处理VS\POST_Raw_Version_LiGong\data\对比文件\原极数据\直线-朝北-快速_RESULT.csv',...
'HeaderLines', 1, 'ReadVariableNames', false);

yuanjidatatemp3 = readtable( 'E:\apersonal\组合算法后处理VS\组合算法后处理VS\POST_Raw_Version_LiGong\data\对比文件\原极数据\换位置-第一次绕圈，匀速_RESULT.csv',...
'HeaderLines', 1, 'ReadVariableNames', false);

yuanjidatatemp4 = readtable( 'E:\apersonal\组合算法后处理VS\组合算法后处理VS\POST_Raw_Version_LiGong\data\对比文件\原极数据\直线-朝北-慢速_RESULT.csv',...
'HeaderLines', 1, 'ReadVariableNames', false);
yuanjidatatemp5 = readtable( 'E:\apersonal\组合算法后处理VS\组合算法后处理VS\POST_Raw_Version_LiGong\data\对比文件\原极数据\直线-快速_RESULT.csv',...
'HeaderLines', 1, 'ReadVariableNames', false);


feed_eddb(feedback,feedback1);
feed_eddb(feedback2,feedback3);
feed_eddb(feedback4,feedback5);
feed_eddb(feedback6,feedback7);
%位置优化对比
figure;
subplot(3, 2, 1);guiji(ResDatatemp, ResDatatemp1,yuanjidatatemp,'1' );
subplot(3, 2, 2);guiji(ResDatatemp2, ResDatatemp3, yuanjidatatemp1,1);
subplot(3, 2, 3);guiji(ResDatatemp4, ResDatatemp5, yuanjidatatemp2,1);
subplot(3, 2, 4);guiji(ResDatatemp6, ResDatatemp7, yuanjidatatemp3,1);
subplot(3, 2, 5);guiji(ResDatatemp8, ResDatatemp9, yuanjidatatemp4,1);
subplot(3, 2, 6);guiji(ResDatatemp10, ResDatatemp11, yuanjidatatemp5,1);
weizhiduibi(ResDatatemp,ResDatatemp1,yuanjidatatemp,0);
weizhiduibi(ResDatatemp2,ResDatatemp3,yuanjidatatemp1,0);%绕圈快速
weizhiduibi(ResDatatemp10,ResDatatemp11,yuanjidatatemp5,0)

veeldubi(ResDatatemp,ResDatatemp1,yuanjidatatemp,0);

zitaiduibi(ResDatatemp,ResDatatemp1,yuanjidatatemp,1);
zitaiduibi(ResDatatemp2,ResDatatemp3,yuanjidatatemp1,1);
zitaiduibi(ResDatatemp10,ResDatatemp11,yuanjidatatemp5,1);
zitaiduibi(ResDatatemp6, ResDatatemp7, yuanjidatatemp3,1);
function weizhiduibi(ResDatatemp,ResDatatemp2,yuanjidatatemp,flag)
DEG2RAD = pi/180;
R = 6378137;  % 地球半径(米)
common=find(ResDatatemp{:,35}~=0);
commonyuji=find(yuanjidatatemp{:,24}~=0);
[m61x,m61y,m61z]=llh2enu(ResDatatemp,25,26,27,R,DEG2RAD);

[m61x1,m61y1,m61z1]=llh2enu(ResDatatemp2,25,26,27,R,DEG2RAD);

[yjx,yjy,yjz]=llh2enu(yuanjidatatemp,6,5,7,R,DEG2RAD);
[m61_gnssx,m61_gnssy,m61_gnssz]=llh2enu2(ResDatatemp{common,34},ResDatatemp{common,35},ResDatatemp{common,36},ResDatatemp{common(1),34},ResDatatemp{common(1),35},ResDatatemp{common(1),36},R,DEG2RAD);
[yj_gnssx,yj_gnssy,yj_gnssz]=llh2enu2(yuanjidatatemp{commonyuji,25},yuanjidatatemp{commonyuji,24},yuanjidatatemp{commonyuji,26},yuanjidatatemp{commonyuji(1),25},yuanjidatatemp{commonyuji(1),24},yuanjidatatemp{commonyuji(1),26},R,DEG2RAD);

%天向对比
figure;hold on;subplot(3, 2, 1);
plot(1:length(ResDatatemp{:,27}),m61x,'.r-', 'DisplayName','优化前');hold on
plot(1:length(ResDatatemp2{:,27}),m61x1,'.g-', 'DisplayName','优化后');hold on
plot(common,m61_gnssx,'.b-', 'DisplayName','gnss');hold on;
xlabel('历元'); ylabel('x(m)'); title('ins600m21A东向误差分析'); legend;
if(flag==1)
yyaxis right;
plot((1:length(yuanjidatatemp{:,5}))*2,yjx,'.k', 'DisplayName','算法结果');hold on
end
subplot(3, 2, 2);
diff61x=m61x(common)-m61_gnssx;

diff61x1=m61x1(common)-m61_gnssx;

plot(common,diff61x,'.r-', 'DisplayName','优化前东向差值');hold on;
plot(common,diff61x1,'.b-', 'DisplayName','优化后东向差值');

hold on;subplot(3, 2, 3);
plot(1:length(ResDatatemp{:,27}),m61y,'.r-', 'DisplayName','优化前');hold on
plot(1:length(ResDatatemp2{:,27}),m61y1,'.g-', 'DisplayName','优化后');hold on
plot(common,m61_gnssy,'.b-', 'DisplayName','gnss');hold on;
xlabel('历元'); ylabel('y(m)'); title('ins600m21A北向误差分析'); 
if(flag==1)
yyaxis right;
plot(1:length(yuanjidatatemp{:,5}),yjy,'.k', 'DisplayName','原极算法结果');hold on;legend;
end
subplot(3, 2, 4);
diff61y=m61y(common)-m61_gnssy;diff61y1=m61y1(common)-m61_gnssy;
plot(common,diff61y,'.r-', 'DisplayName','优化前北向差值');hold on;
plot(common,diff61y1,'.b-', 'DisplayName','优化前北向差值');

hold on;subplot(3, 2, 5);
plot(1:length(ResDatatemp{:,27}), m61z, '.r-', 'DisplayName','优化前高度');hold on;
plot(1:length(ResDatatemp2{:,27}), m61z1, '.g-', 'DisplayName','优化后高度');hold on;
plot(common, m61_gnssz, '.b-', 'DisplayName','gnss');hold on;
xlabel('历元'); ylabel('高度(m)'); title('ins600m21A高度分析'); 
if(flag==1)
yyaxis right;
plot(1:length(yuanjidatatemp{:,7}), yjz, '.k', 'DisplayName','算法结果');hold on;
end
legend;
subplot(3, 2, 6);
diff_h=m61z(common)-m61_gnssz;
diff_h1=m61z1(common)-m61_gnssz;
plot(common, diff_h,'.r', 'DisplayName','高度差值');hold on;
plot(common, diff_h1,'.b', 'DisplayName','高度差值');hold on;


end
function veeldubi(ResDatatemp,ResDatatemp2,yuanjidatatemp,flag)
    common=find(ResDatatemp{:,35}~=0);
    figure;hold on;subplot(3, 2, 1);
    plot(1:length(ResDatatemp{:,40}), ResDatatemp{:,40}, '.r', 'DisplayName','优化前算法结果');hold on;
    plot(1:length(ResDatatemp2{:,40}), ResDatatemp2{:,40}, '.g', 'DisplayName','优化后算法结果');hold on;
    plot(common, ResDatatemp{common,37}, '.b', 'DisplayName','gnss');hold on;
    xlabel('历元'); ylabel('Ve(m/s)'); title('ins600m21A动向速度分析');
      if(flag==1) yyaxis right;plot(1:length(yuanjidatatemp{:,13}), yuanjidatatemp{:,13}, '.r', 'DisplayName','算法结果');hold on;end
    legend;
    subplot(3, 2, 2);
    diff_ve=ResDatatemp{common,40}-ResDatatemp{common,37};
    diff_ve1=ResDatatemp2{common,40}-ResDatatemp2{common,37};
    plot(common, diff_ve,'.r', 'DisplayName','优化前');hold on;
    plot(common, diff_ve1,'.b', 'DisplayName','优化后');hold on;
    title("优化前后速度差值对比");
    legend;

    subplot(3, 2, 3);
    plot(1:length(ResDatatemp{:,41}), ResDatatemp{:,41}, '.r', 'DisplayName','优化前算法结果');hold on;
    plot(1:length(ResDatatemp2{:,41}), ResDatatemp2{:,41}, '.g', 'DisplayName','优化后算法结果');hold on;
    plot(common, ResDatatemp{common,38}, '.b', 'DisplayName','gnss');hold on;
    xlabel('历元'); ylabel('Ve(m/s)'); title('ins600m21A北向速度分析'); legend;
    diff_vn=ResDatatemp{common,41}-ResDatatemp{common,38};
    diff_vn1=ResDatatemp2{common,41}-ResDatatemp2{common,38};
    if(flag==1)
        yyaxis right;
        plot(1:length(yuanjidatatemp{:,12}), yuanjidatatemp{:,12}, '.r', 'DisplayName','算法结果');hold on;end
    subplot(3, 2, 4);
    plot(common, diff_vn,'.r','DisplayName','优化前');hold on;
    plot(common, diff_vn1,'.b','DisplayName','优化后');hold on;
    ylabel('北向速度差值(m/s)');legend;
    subplot(3, 2, 5);
    plot(1:length(ResDatatemp{:,42}), ResDatatemp{:,42}, '.r', 'DisplayName','优化前算法结果');hold on;
    plot(1:length(ResDatatemp2{:,42}), ResDatatemp2{:,42}, '.b', 'DisplayName','优化后算法结果');hold on;
    plot(common, ResDatatemp{common,39}, '.b', 'DisplayName','gnss');hold on;
    xlabel('历元'); ylabel('Ve(m/s)'); title('ins600m21A天向速度分析'); 
    if(flag==1)
        yyaxis right;
        plot(1:length(yuanjidatatemp{:,14}), yuanjidatatemp{:,14}, '.r', 'DisplayName','算法结果');hold on;end
    legend;
    subplot(3, 2, 6);
    diff_vu=ResDatatemp{common,42}-ResDatatemp{common,39};
    diff_vu1=ResDatatemp2{common,42}-ResDatatemp2{common,39};
    plot(common, diff_vu,'.r', 'DisplayName','优化前天向速度差值');hold on;
    plot(common, diff_vu1,'.b', 'DisplayName','优化后天向速度差值');hold on;
    ylabel('北向速度差值(m/s)');legend;
    hold off;
end
function zitaiduibi(ResDatatemp,ResDatatemp2,yuanjidatatemp,flag)
    common=find(ResDatatemp{:,35}~=0);
    figure;hold on;subplot(3, 1, 1);
    plot(1:length(ResDatatemp{:,31}), ResDatatemp{:,31}, '.r', 'DisplayName','优化前算法结果');hold on;
    plot(1:length(ResDatatemp2{:,31}), ResDatatemp2{:,31}, '.g', 'DisplayName','优化后算法结果');hold on;
    plot(common, ResDatatemp{common,28}, '.b', 'DisplayName','gnss');hold on;
    xlabel('历元'); ylabel('Pitch(°)'); title('ins600m21A Pitch分析');
    if(flag==1)
    plot(1:length(yuanjidatatemp{:,9}), yuanjidatatemp{:,9}, '.k', 'DisplayName','原机算法结果');hold on;end
    legend;
    subplot(3, 1, 2);
    plot(1:length(ResDatatemp{:,32}), ResDatatemp{:,32}, '.r', 'DisplayName','优化前算法结果');hold on;
    plot(1:length(ResDatatemp2{:,32}), ResDatatemp2{:,32}, '.g', 'DisplayName','优化后算法结果');hold on;
    plot(common, ResDatatemp{common,29}, '.b', 'DisplayName','gnss');hold on;
    xlabel('历元'); ylabel('roll(°)'); title('ins600m21A roll(°)'); 
     if(flag==1)plot(1:length(yuanjidatatemp{:,10}), yuanjidatatemp{:,10}, '.k', 'DisplayName','原机算法结果');hold on;end
    legend;
    subplot(3, 1, 3);
    plot(1:length(ResDatatemp{:,33}), ResDatatemp{:,33}, '.r', 'DisplayName','优化前算法结果');hold on;
    plot(1:length(ResDatatemp2{:,33}), ResDatatemp2{:,33}, '.g', 'DisplayName','优化后算法结果');hold on;
    plot(common, ResDatatemp{common,30}, '.b', 'DisplayName','gnss');hold on;
    xlabel('历元'); ylabel('heading(°)'); title('ins600m21A Heading分析'); 
     if(flag==1)plot(1:length(yuanjidatatemp{:,8}), yuanjidatatemp{:,8}, '.k', 'DisplayName','原机算法结果');hold on;end
    legend;
end
function [E, N, U] = llh2enu(resdata,lat_deg, lon_deg, h,  R, DEG2RAD)
    lat_rad=resdata{:,lat_deg}* DEG2RAD;
    lon_rad = resdata{:,lon_deg}* DEG2RAD;
    h_rad=resdata{:,h};

    lat0_rad=resdata{1,lat_deg}* DEG2RAD;
    lon0_rad=resdata{1,lon_deg}* DEG2RAD;
    h0=resdata{1,h};

   % lat_rad = lat_deg(:) * DEG2RAD;  % 转为列向量+弧度
   % lon_rad = lon_deg(:) * DEG2RAD;
    dLat = lat_rad - lat0_rad;
    dLon = lon_rad - lon0_rad;
    E = (R + h_rad(:)) .* cos(lat0_rad) .* dLon;
    N = (R + h_rad(:)) .* dLat;
    U = h_rad(:) - h0;
end
function [E, N, U] = llh2enu2(lat_deg, lon_deg, h, lat0_rad, lon0_rad, h0, R, DEG2RAD)
    lat0_rad=lat0_rad* DEG2RAD;
    lon0_rad=lon0_rad* DEG2RAD;
    lat_rad = lat_deg(:) * DEG2RAD;  % 转为列向量+弧度
    lon_rad = lon_deg(:) * DEG2RAD;
    dLat = lat_rad - lat0_rad;
    dLon = lon_rad - lon0_rad;
    E = (R + h(:) ).* cos(lat0_rad) .* dLon;
    N = (R + h(:)) .* dLat;
    U = h(:) - h0;
end
function guiji(data1, data2, yuanjidatatemp,flag)
    plot(data1{:,26}, data1{:,25}, '.-r', 'DisplayName', '优化前'); hold on;
   % hold(ax, 'on');    
    plot(data2{:,26}, data2{:,25}, '.-g', 'DisplayName', '优化后'); hold on;
    if(flag==1) plot(yuanjidatatemp{:,5},yuanjidatatemp{:,6}, '.-k', 'DisplayName','原极算法结果');hold on;end
    plot(data1{data1{:,35} ~= 0, 35}, data1{data1{:,34} ~= 0, 34}, '.-b', 'DisplayName', 'GNSS');
    xlabel('经度(°)');  ylabel('纬度(°)'); title('优化前后轨迹对比'); 
    legend( 'Location', 'best');
    grid( 'on');
end
function feed_eddb(feeb,feedafter)
    common=find(feeb{:,16}~=0);
    figure;hold on;subplot(3, 2, 1);
    plot(1:length(feeb{common,16}), feeb{common,16}, '.r', 'DisplayName','优化前算法结果');hold on;
    plot(1:length(feedafter{common,16}), feedafter{common,16}, '.g', 'DisplayName','优化后算法结果');hold on;    
    xlabel('历元');  title('优化前后陀螺零偏变化'); 
    legend( 'Location', 'best');

    hold on;subplot(3, 2, 2);
    plot(1:length(feeb{common,19}), feeb{common,19}, '.r', 'DisplayName','优化前算法结果');hold on;
    plot(1:length(feedafter{common,19}), feedafter{common,19}, '.g', 'DisplayName','优化后算法结果');hold on;  
    xlabel('历元');  title('优化前后加计零偏变化'); 
    legend( 'Location', 'best');

    hold on;subplot(3, 2, 3);
    plot(1:length(feeb{common,17}), feeb{common,17}, '.r', 'DisplayName','优化前算法结果');hold on;
    plot(1:length(feedafter{common,17}), feedafter{common,17}, '.g', 'DisplayName','优化后算法结果');hold on;    
    xlabel('历元');  title('优化前后陀螺零偏变化'); 
    legend( 'Location', 'best');
    hold on;subplot(3, 2, 4);
    plot(1:length(feeb{common,20}), feeb{common,20}, '.r', 'DisplayName','优化前算法结果');hold on;
    plot(1:length(feedafter{common,20}), feedafter{common,20}, '.g', 'DisplayName','优化后算法结果');hold on;   
    xlabel('历元');   title('优化前后加计零偏变化'); 
    legend( 'Location', 'best');

    hold on;subplot(3, 2, 5);
    plot(1:length(feeb{common,18}), feeb{common,18}, '.r', 'DisplayName','优化前算法结果');hold on;
    plot(1:length(feedafter{common,18}), feedafter{common,18}, '.g', 'DisplayName','优化后算法结果');hold on;   
    xlabel('历元');  title('优化前后陀螺零偏变化'); 
    legend( 'Location', 'best');

    hold on;subplot(3, 2, 6);
    plot(1:length(feeb{common,21}), feeb{common,21}, '.r', 'DisplayName','优化前算法结果');hold on;
    plot(1:length(feedafter{common,21}), feedafter{common,21}, '.g', 'DisplayName','优化后算法结果');hold on;    
    xlabel('历元');   title('优化前后加计零偏变化'); 
    legend( 'Location', 'best');
end
